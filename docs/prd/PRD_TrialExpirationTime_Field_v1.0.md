# PRD - 添加独立的试用到期时间字段

## 1. 文档信息

| 项目 | 内容 |
|------|------|
| 文档版本 | v1.0 |
| 创建日期 | 2025-01-15 |
| 负责人 | Emma (产品经理) |
| 最后更新 | 2025-01-15 |
| 状态 | 待开发 |

## 2. 背景与问题陈述

### 2.1 业务背景
当前系统中，试用到期时间和VIP时间共用同一个字段（`vipTime`），这导致了概念混淆和业务逻辑不清晰的问题。

### 2.2 核心问题
1. **概念混淆**：试用期和VIP会员是两个不同的概念，不应该共用字段
2. **状态不明**：无法清晰区分用户当前是试用状态还是正式VIP状态
3. **逻辑冲突**：试用到期时间会覆盖VIP时间，或者被VIP时间阻止设置
4. **业务复杂**：需要通过复杂的比较逻辑来判断哪个时间更重要

### 2.3 当前实现问题
```java
// 当前的问题代码
if (user.getVipTime() != null && user.getVipTime().after(expirationTime)) {
    // 如果VIP时间更长，就不设置试用时间
    return;
}
user.setVipTime(expirationTime); // 直接覆盖VIP时间
```

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
1. **概念清晰化**：试用期和VIP会员有独立的字段和逻辑
2. **状态明确化**：用户状态可以清晰区分（试用中、VIP中、已过期等）
3. **逻辑简化**：移除复杂的时间比较逻辑
4. **扩展性增强**：为未来的会员体系扩展提供基础

### 3.2 关键结果 (Key Results)
- 添加独立的 `trialExpirationTime` 字段
- 用户状态判断准确率达到100%
- 试用和VIP逻辑完全分离
- 向后兼容性保持100%

### 3.3 反向指标 (Counter Metrics)
- 数据迁移过程中零数据丢失
- 系统性能不降低
- API接口向后兼容

## 4. 用户画像与用户故事

### 4.1 目标用户
- **代理商**：需要为用户开通试用权限
- **最终用户**：需要了解自己的试用状态和VIP状态
- **系统管理员**：需要清晰的用户状态管理

### 4.2 用户故事
**作为代理商**，我希望为用户开通试用时，不会影响用户已有的VIP权限，这样用户就能同时享受试用和VIP的权益。

**作为最终用户**，我希望能清楚地知道我的试用期何时到期，VIP会员何时到期，这样我就能合理安排我的学习计划。

**作为系统管理员**，我希望能清晰地区分用户的试用状态和VIP状态，这样我就能提供更精准的用户服务。

## 5. 功能规格详述

### 5.1 数据库设计

#### 5.1.1 新增字段
在 `inz_user_front` 表中新增：
```sql
ALTER TABLE inz_user_front 
ADD COLUMN trial_expiration_time DATETIME COMMENT '试用到期时间';
```

#### 5.1.2 字段说明
- **trial_expiration_time**: 试用到期时间
  - 类型：DATETIME
  - 可空：是
  - 默认值：NULL
  - 说明：用户试用权限的到期时间，与VIP时间独立

### 5.2 业务逻辑设计

#### 5.2.1 用户状态判断逻辑
```java
public enum UserStatus {
    TRIAL_ACTIVE,      // 试用中
    VIP_ACTIVE,        // VIP中  
    BOTH_ACTIVE,       // 试用和VIP都有效
    EXPIRED,           // 已过期
    NORMAL             // 普通用户
}

public UserStatus getUserStatus(InzUserFront user) {
    Date now = new Date();
    boolean trialValid = user.getTrialExpirationTime() != null && 
                        user.getTrialExpirationTime().after(now);
    boolean vipValid = user.getVipTime() != null && 
                      user.getVipTime().after(now);
    
    if (trialValid && vipValid) return UserStatus.BOTH_ACTIVE;
    if (trialValid) return UserStatus.TRIAL_ACTIVE;
    if (vipValid) return UserStatus.VIP_ACTIVE;
    if (user.getTrialExpirationTime() != null || user.getVipTime() != null) {
        return UserStatus.EXPIRED;
    }
    return UserStatus.NORMAL;
}
```

#### 5.2.2 试用到期时间设置逻辑
```java
private void setTrialExpirationTime(String userId, Integer trialDays) {
    InzUserFront user = inzUserFrontService.getById(userId);
    if (user == null) return;
    
    // 计算试用到期时间：当前时间 + 试用天数
    Calendar calendar = Calendar.getInstance();
    calendar.add(Calendar.DAY_OF_MONTH, trialDays);
    Date expirationTime = calendar.getTime();
    
    // 直接设置试用到期时间，不与VIP时间比较
    user.setTrialExpirationTime(expirationTime);
    inzUserFrontService.updateById(user);
    
    log.info("试用到期时间设置成功 - 用户ID: {}, 试用天数: {}天, 到期时间: {}",
            userId, trialDays, expirationTime);
}
```

### 5.3 API接口设计

#### 5.3.1 用户状态查询接口
```java
@GetMapping("/getUserStatus")
public Result<UserStatusVO> getUserStatus(@RequestParam String userId) {
    InzUserFront user = inzUserFrontService.getById(userId);
    UserStatusVO statusVO = new UserStatusVO();
    statusVO.setUserId(userId);
    statusVO.setTrialExpirationTime(user.getTrialExpirationTime());
    statusVO.setVipTime(user.getVipTime());
    statusVO.setStatus(getUserStatus(user));
    statusVO.setTrialRemainingDays(user.getTrialRemainingDays());
    return Result.OK(statusVO);
}
```

#### 5.3.2 响应数据结构
```java
public class UserStatusVO {
    private String userId;
    private Date trialExpirationTime;    // 试用到期时间
    private Date vipTime;                // VIP到期时间
    private UserStatus status;           // 用户状态
    private Integer trialRemainingDays;  // 剩余试用天数
    private Boolean hasActivePermission; // 是否有有效权限
}
```

## 6. 范围定义

### 6.1 包含功能 (In Scope)
- 添加 `trial_expiration_time` 字段
- 修改试用到期时间设置逻辑
- 新增用户状态判断方法
- 更新相关API接口
- 数据迁移脚本

### 6.2 排除功能 (Out of Scope)
- 前端界面的重新设计
- 历史数据的重新计算
- 复杂的会员等级体系
- 第三方系统集成

## 7. 数据迁移方案

### 7.1 迁移策略
1. **新增字段**：先添加新字段，默认为NULL
2. **数据迁移**：将现有的试用相关数据迁移到新字段
3. **逻辑切换**：逐步切换到新的业务逻辑
4. **清理验证**：验证数据一致性

### 7.2 迁移脚本
```sql
-- 1. 添加新字段
ALTER TABLE inz_user_front 
ADD COLUMN trial_expiration_time DATETIME COMMENT '试用到期时间';

-- 2. 数据迁移（需要根据业务逻辑确定）
-- 这里需要根据实际业务情况来确定哪些vipTime是试用时间
UPDATE inz_user_front u
SET trial_expiration_time = u.vip_time
WHERE EXISTS (
    SELECT 1 FROM inz_user_trial_log t 
    WHERE t.user_id = u.id 
    AND t.create_time >= DATE_SUB(u.vip_time, INTERVAL 30 DAY)
);
```

## 8. 依赖与风险

### 8.1 内部依赖
- 数据库表结构变更
- InzUserFront实体类更新
- 相关Service和Controller修改

### 8.2 潜在风险
- **数据迁移风险**：现有数据可能无法准确区分试用和VIP
- **兼容性风险**：现有API可能需要调整
- **业务逻辑风险**：新旧逻辑切换过程中可能出现不一致

### 8.3 风险缓解措施
- 详细的数据分析和迁移测试
- 分阶段部署和验证
- 完整的回滚方案
- 充分的测试覆盖

## 9. 发布计划

### 9.1 开发阶段
- **阶段1**：数据库字段添加和实体类更新
- **阶段2**：业务逻辑修改和API更新
- **阶段3**：数据迁移和验证
- **阶段4**：测试和优化

### 9.2 测试计划
- 单元测试：新增业务逻辑测试
- 集成测试：端到端流程验证
- 数据测试：迁移数据准确性验证
- 兼容性测试：现有功能不受影响

### 9.3 上线计划
- 灰度发布：先在测试环境验证
- 数据备份：完整备份现有数据
- 监控指标：实时监控新字段使用情况
- 回滚预案：准备快速回滚机制

## 10. 版本历史

| 版本 | 日期 | 变更内容 | 负责人 |
|------|------|----------|--------|
| v1.0 | 2025-01-15 | 初始版本创建 | Emma |
