# PRD - 准确的试用流程管理机制

## 1. 文档信息

| 项目 | 内容 |
|------|------|
| 文档版本 | v2.0 |
| 创建日期 | 2025-01-15 |
| 负责人 | Emma (产品经理) |
| 最后更新 | 2025-01-15 |
| 状态 | 待开发 |

## 2. 背景与问题陈述

### 2.1 准确的业务流程
根据老板提供的准确流程，系统应该按以下逻辑运行：

1. **用户注册** → 获得 `trialRemainingDays`（剩余可用体验天数）
2. **代理商开通体验** → 
   - 用户变为"已试学状态"(`tryStudy = "1"`)
   - 可使用特定education词书
   - `trialRemainingDays`减少
   - 记录本次开通的到期时间
3. **体验到期** → 无法使用开通的education词书
4. **开通全年权限** → 用户变为"正式用户"(`formalUser = "1"`)

### 2.2 当前实现的问题
- 缺少记录特定education开通的到期时间
- 没有正确设置`tryStudy`状态
- 没有区分试用权限和正式用户权限
- 缺少education级别的权限管理

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
1. **状态管理准确**：正确设置用户的`tryStudy`和`formalUser`状态
2. **权限精确控制**：按education维度管理试用权限和到期时间
3. **流程完整性**：完整实现从注册→试用→正式用户的流程
4. **数据一致性**：确保所有状态和权限数据的一致性

### 3.2 关键结果 (Key Results)
- 用户状态设置准确率达到100%
- Education权限控制精确率达到100%
- 试用到期检查准确率达到100%
- 正式用户转换成功率达到100%

## 4. 用户画像与用户故事

### 4.1 核心用户故事

**作为新注册用户**，我希望获得一定的体验天数，这样我就能让代理商为我开通感兴趣的education进行试学。

**作为代理商**，我希望为用户开通特定education的试用权限时，系统能：
- 自动减少用户的剩余体验天数
- 设置用户为"已试学状态"
- 记录这个education的到期时间
- 让用户只能使用这个education的词书

**作为试学用户**，我希望在试用期内能正常使用开通的education词书，到期后系统能自动限制我的访问。

**作为代理商**，我希望为用户开通全年权限时，用户能变为正式用户，拥有完整的权限。

## 5. 功能规格详述

### 5.1 数据模型设计

#### 5.1.1 用户状态字段
```sql
-- inz_user_front表中的关键字段
tryStudy VARCHAR(1) DEFAULT '0' COMMENT '是否处于试学期（1是，0否）'
formalUser VARCHAR(1) DEFAULT '0' COMMENT '是否为正式用户（1是，0否）'
trialRemainingDays INT DEFAULT 0 COMMENT '剩余可用试用天数'
vipTime DATETIME COMMENT '会员到期时间（正式用户）'
```

#### 5.1.2 Education权限记录表
需要新增表来记录用户对特定education的权限：
```sql
CREATE TABLE inz_user_education_permission (
    id VARCHAR(32) PRIMARY KEY,
    user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
    education_id VARCHAR(32) NOT NULL COMMENT '教育系列ID',
    permission_type TINYINT NOT NULL COMMENT '权限类型：1=试用，2=正式',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    status TINYINT DEFAULT 1 COMMENT '状态：1=有效，0=无效',
    granted_by VARCHAR(32) COMMENT '授权人ID',
    trial_days_used INT COMMENT '消耗的试用天数',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_education (user_id, education_id),
    INDEX idx_end_time (end_time),
    INDEX idx_status (status)
);
```

### 5.2 业务流程设计

#### 5.2.1 用户注册流程
```java
// 用户注册时设置初始状态
user.setTryStudy("0");           // 未试学
user.setFormalUser("0");         // 非正式用户
user.setTrialRemainingDays(30);  // 默认30天体验时间
```

#### 5.2.2 代理商开通试用流程
```java
@Transactional(rollbackFor = Exception.class)
public boolean grantTrialPermission(String userId, String educationId, Integer days) {
    // 1. 验证剩余天数
    InzUserFront user = userService.getById(userId);
    if (user.getTrialRemainingDays() < days) {
        throw new BusinessException("剩余试用天数不足");
    }
    
    // 2. 减少剩余天数
    user.setTrialRemainingDays(user.getTrialRemainingDays() - days);
    user.setTryStudy("1"); // 设置为试学状态
    userService.updateById(user);
    
    // 3. 创建education权限记录
    InzUserEducationPermission permission = new InzUserEducationPermission();
    permission.setUserId(userId);
    permission.setEducationId(educationId);
    permission.setPermissionType(1); // 试用
    permission.setStartTime(new Date());
    permission.setEndTime(DateUtils.addDays(new Date(), days));
    permission.setTrialDaysUsed(days);
    permission.setGrantedBy(getCurrentUserId());
    permissionService.save(permission);
    
    // 4. 记录试用日志
    recordTrialLog(userId, educationId, days);
    
    return true;
}
```

#### 5.2.3 开通全年权限流程
```java
@Transactional(rollbackFor = Exception.class)
public boolean grantAnnualPermission(String userId, String educationId) {
    // 1. 扣除金豆等验证
    
    // 2. 设置用户为正式用户
    InzUserFront user = userService.getById(userId);
    user.setFormalUser("1");
    user.setVipTime(DateUtils.addYears(new Date(), 1));
    userService.updateById(user);
    
    // 3. 创建正式权限记录
    InzUserEducationPermission permission = new InzUserEducationPermission();
    permission.setUserId(userId);
    permission.setEducationId(educationId);
    permission.setPermissionType(2); // 正式
    permission.setStartTime(new Date());
    permission.setEndTime(DateUtils.addYears(new Date(), 1));
    permission.setGrantedBy(getCurrentUserId());
    permissionService.save(permission);
    
    return true;
}
```

#### 5.2.4 权限验证流程
```java
public boolean hasEducationPermission(String userId, String educationId) {
    // 1. 检查是否为正式用户
    InzUserFront user = userService.getById(userId);
    if ("1".equals(user.getFormalUser()) && user.getVipTime() != null && user.getVipTime().after(new Date())) {
        return true; // 正式用户有所有权限
    }
    
    // 2. 检查特定education的试用权限
    List<InzUserEducationPermission> permissions = permissionService.lambdaQuery()
        .eq(InzUserEducationPermission::getUserId, userId)
        .eq(InzUserEducationPermission::getEducationId, educationId)
        .eq(InzUserEducationPermission::getStatus, 1)
        .gt(InzUserEducationPermission::getEndTime, new Date())
        .list();
    
    return !permissions.isEmpty();
}
```

### 5.3 用户状态枚举
```java
public enum UserStatus {
    NORMAL("normal", "普通用户", "0", "0"),
    TRIAL("trial", "试学用户", "1", "0"),
    FORMAL("formal", "正式用户", "0", "1"),
    TRIAL_FORMAL("trial_formal", "试学+正式用户", "1", "1");
    
    private final String code;
    private final String description;
    private final String tryStudy;
    private final String formalUser;
}
```

## 6. API接口设计

### 6.1 权限管理接口
```java
// 检查用户对特定education的权限
GET /api/permission/check?userId={userId}&educationId={educationId}

// 获取用户的所有education权限
GET /api/permission/list?userId={userId}

// 获取用户状态信息
GET /api/user/status?userId={userId}
```

### 6.2 响应数据结构
```java
public class UserEducationPermissionVO {
    private String educationId;
    private String educationName;
    private Integer permissionType; // 1=试用，2=正式
    private Date startTime;
    private Date endTime;
    private Boolean isActive;
    private Long remainingDays;
}

public class UserStatusVO {
    private String userId;
    private String tryStudy;
    private String formalUser;
    private Integer trialRemainingDays;
    private Date vipTime;
    private List<UserEducationPermissionVO> educationPermissions;
    private UserStatus status;
}
```

## 7. 范围定义

### 7.1 包含功能 (In Scope)
- 新增用户education权限记录表
- 完善用户状态管理（tryStudy、formalUser）
- 实现education级别的权限控制
- 优化代理商开通试用流程
- 完善全年权限开通流程
- 权限验证和到期检查机制

### 7.2 排除功能 (Out of Scope)
- 前端界面的重新设计
- 历史数据的批量修复
- 复杂的权限继承机制
- 第三方系统集成

## 8. 依赖与风险

### 8.1 内部依赖
- 新增数据库表结构
- Education实体和服务
- 用户权限验证机制
- 试用日志记录系统

### 8.2 潜在风险
- **数据迁移风险**：新增表结构需要数据迁移
- **权限复杂性**：多层权限验证可能影响性能
- **状态一致性**：多个状态字段需要保持一致

### 8.3 风险缓解措施
- 分阶段部署和验证
- 完整的权限缓存机制
- 详细的状态同步逻辑
- 充分的测试覆盖

## 9. 发布计划

### 9.1 开发阶段
- **阶段1**：数据库表结构设计和创建
- **阶段2**：用户状态管理优化
- **阶段3**：Education权限控制实现
- **阶段4**：权限验证和到期检查
- **阶段5**：测试和优化

### 9.2 测试计划
- 单元测试：权限逻辑、状态管理
- 集成测试：完整业务流程
- 权限测试：各种权限场景验证
- 性能测试：权限验证性能

## 10. 版本历史

| 版本 | 日期 | 变更内容 | 负责人 |
|------|------|----------|--------|
| v2.0 | 2025-01-15 | 基于准确流程重新设计 | Emma |
| v1.0 | 2025-01-15 | 初始版本（已废弃） | Emma |
