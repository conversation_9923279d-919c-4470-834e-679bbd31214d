# PRD - trialRemainingDays剩余体验天数管理机制

## 1. 文档信息

| 项目 | 内容 |
|------|------|
| 文档版本 | v1.0 |
| 创建日期 | 2025-01-15 |
| 负责人 | Emma (产品经理) |
| 最后更新 | 2025-01-15 |
| 状态 | 待开发 |

## 2. 背景与问题陈述

### 2.1 业务背景
当前系统中，用户拥有trialRemainingDays（剩余可支配的体验天数），代理商可以为其管辖范围内的用户开通体验权限。但在代理商为用户开通体验后，需要确保：
1. 用户的剩余可支配体验天数会相应减少
2. 所有操作都有完整的日志记录
3. 数据一致性得到保障

### 2.2 核心问题
- 代理商开通体验后，用户的trialRemainingDays需要实时减少
- 需要详细的操作日志记录，包括操作者、操作时间、变更前后的天数等
- 需要防止超额分配（分配天数不能超过用户剩余可用天数）
- 需要确保数据一致性和操作的原子性

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
1. **数据准确性**：确保trialRemainingDays在代理商操作后准确减少
2. **操作透明性**：所有操作都有完整的审计日志
3. **业务合规性**：防止超额分配，确保业务规则得到严格执行
4. **系统稳定性**：操作具有原子性，避免数据不一致

### 3.2 关键结果 (Key Results)
- trialRemainingDays减少准确率达到100%
- 操作日志记录完整率达到100%
- 超额分配防护成功率达到100%
- 数据一致性检查通过率达到100%

### 3.3 反向指标 (Counter Metrics)
- 系统响应时间不超过当前基准的120%
- 数据库事务失败率不超过0.1%

## 4. 用户画像与用户故事

### 4.1 目标用户
- **代理商**：需要为其管辖用户开通体验权限
- **系统管理员**：需要监控和审计所有操作
- **最终用户**：体验权限的受益者

### 4.2 用户故事
**作为代理商**，我希望为用户开通体验时，系统能自动减少用户的剩余可支配天数，这样我就能准确掌握用户的可用资源。

**作为系统管理员**，我希望所有的天数变更操作都有详细的日志记录，这样我就能进行有效的审计和问题追踪。

**作为最终用户**，我希望我的剩余体验天数信息是准确和实时的，这样我就能合理规划我的学习计划。

## 5. 功能规格详述

### 5.1 核心业务流程

#### 5.1.1 代理商开通体验流程
```mermaid
sequenceDiagram
    participant Agent as 代理商
    participant System as 系统
    participant DB as 数据库
    participant Log as 日志系统

    Agent->>System: 请求为用户开通体验
    System->>DB: 查询用户当前剩余天数
    System->>System: 验证分配天数是否超额
    alt 天数充足
        System->>DB: 开始事务
        System->>DB: 减少用户trialRemainingDays
        System->>Log: 记录操作日志
        System->>DB: 创建体验权限记录
        System->>DB: 提交事务
        System->>Agent: 返回成功结果
    else 天数不足
        System->>Agent: 返回错误信息
    end
```

### 5.2 详细功能规格

#### 5.2.1 天数减少机制
- **触发条件**：代理商成功为用户开通体验权限
- **减少逻辑**：trialRemainingDays = 当前剩余天数 - 开通天数
- **最小值限制**：减少后的天数不能小于0
- **原子性保证**：天数减少与权限开通在同一事务中执行

#### 5.2.2 日志记录机制
- **记录时机**：每次trialRemainingDays发生变更时
- **记录内容**：
  - 操作者ID（代理商ID）
  - 目标用户ID
  - 操作类型（减少/增加）
  - 变更天数
  - 变更前天数
  - 变更后天数
  - 操作时间
  - 操作描述
  - 关联的教育系列ID（如适用）

#### 5.2.3 数据验证机制
- **前置验证**：
  - 验证代理商权限
  - 验证用户存在性
  - 验证剩余天数充足性
- **后置验证**：
  - 验证数据更新成功
  - 验证日志记录完整

## 6. 范围定义

### 6.1 包含功能 (In Scope)
- trialRemainingDays自动减少机制
- 完整的操作日志记录
- 超额分配防护
- 数据一致性保障
- 事务原子性保证

### 6.2 排除功能 (Out of Scope)
- 用户界面的重新设计
- 新的权限管理体系
- 历史数据的批量修复
- 第三方系统集成

## 7. 依赖与风险

### 7.1 内部依赖
- InzUserTrialLogService：试用记录服务
- PermissionGrantController：权限授予控制器
- InzUserFrontService：用户服务
- 数据库事务管理

### 7.2 外部依赖
- 无

### 7.3 潜在风险
- **数据一致性风险**：并发操作可能导致数据不一致
- **性能风险**：频繁的数据库操作可能影响性能
- **业务逻辑风险**：复杂的计算逻辑可能出现边界情况

### 7.4 风险缓解措施
- 使用数据库事务确保原子性
- 添加数据库锁机制防止并发问题
- 完善的单元测试覆盖边界情况
- 详细的错误日志记录

## 8. 发布初步计划

### 8.1 开发阶段
- **阶段1**：完善现有的reduceTrialDays方法
- **阶段2**：增强日志记录机制
- **阶段3**：添加数据一致性检查
- **阶段4**：完善错误处理和回滚机制

### 8.2 测试计划
- 单元测试：覆盖所有业务逻辑
- 集成测试：验证端到端流程
- 压力测试：验证并发场景
- 数据一致性测试：验证事务完整性

### 8.3 上线计划
- 灰度发布：先在测试环境验证
- 监控指标：实时监控天数变更和日志记录
- 回滚预案：准备快速回滚机制

## 9. 版本历史

| 版本 | 日期 | 变更内容 | 负责人 |
|------|------|----------|--------|
| v1.0 | 2025-01-15 | 初始版本创建 | Emma |
