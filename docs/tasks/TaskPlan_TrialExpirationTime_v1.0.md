# 任务规划 - 添加独立的试用到期时间字段

## 1. 任务概览

| 项目 | 内容 |
|------|------|
| 任务名称 | 添加trial_expiration_time字段 |
| 优先级 | 高 |
| 预计工期 | 2-3个工作日 |
| 负责团队 | Bob (架构师) + <PERSON> (工程师) |
| 关联PRD | PRD_TrialExpirationTime_Field_v1.0.md |

## 2. 任务分解

### 2.1 数据库设计任务 (<PERSON>负责)

#### 任务2.1.1：数据库表结构设计
- **描述**：设计新增字段的数据库结构
- **输入**：Emma的PRD文档
- **输出**：数据库变更脚本、字段设计文档
- **预计时间**：2小时
- **关键点**：
  - 字段类型和约束设计
  - 索引策略
  - 兼容性考虑

#### 任务2.1.2：数据迁移策略设计
- **描述**：设计现有数据的迁移方案
- **输入**：现有数据分析
- **输出**：数据迁移脚本、回滚方案
- **预计时间**：3小时
- **关键点**：
  - 识别哪些vipTime是试用时间
  - 数据一致性保障
  - 迁移验证方案

#### 任务2.1.3：业务逻辑架构设计
- **描述**：设计新的用户状态判断和管理逻辑
- **输入**：业务需求分析
- **输出**：业务逻辑架构图、状态机设计
- **预计时间**：2小时
- **关键点**：
  - 用户状态枚举设计
  - 状态转换逻辑
  - 权限判断机制

### 2.2 代码实现任务 (Alex负责)

#### 任务2.2.1：实体类和数据库层修改
- **描述**：更新InzUserFront实体类，添加新字段
- **文件路径**：
  - `InzUserFront.java`
  - `InzUserFrontMapper.xml`
- **预计时间**：1小时
- **具体内容**：
  - 添加trialExpirationTime字段
  - 更新相关注解
  - 添加getter/setter方法

#### 任务2.2.2：业务逻辑层实现
- **描述**：实现新的用户状态判断和试用时间设置逻辑
- **文件路径**：
  - `InzUserFrontService.java`
  - `InzUserFrontServiceImpl.java`
  - `PermissionGrantController.java`
- **预计时间**：4小时
- **具体内容**：
  - 实现getUserStatus方法
  - 修改setTrialExpirationTime方法
  - 添加用户状态查询接口
  - 更新相关业务逻辑

#### 任务2.2.3：数据迁移脚本执行
- **描述**：执行数据库变更和数据迁移
- **预计时间**：2小时
- **具体内容**：
  - 执行DDL脚本添加字段
  - 执行数据迁移脚本
  - 验证迁移结果
  - 创建必要的索引

#### 任务2.2.4：API接口更新
- **描述**：更新相关API接口，支持新的字段和逻辑
- **文件路径**：
  - `PermissionGrantController.java`
  - 新增`UserStatusController.java`
- **预计时间**：3小时
- **具体内容**：
  - 更新现有接口返回数据
  - 新增用户状态查询接口
  - 更新API文档
  - 添加响应VO类

#### 任务2.2.5：单元测试编写
- **描述**：为新增功能编写完整的单元测试
- **测试覆盖**：
  - 用户状态判断逻辑测试
  - 试用时间设置测试
  - 数据迁移验证测试
  - API接口测试
- **预计时间**：3小时
- **测试工具**：JUnit + Mockito + Playwright

#### 任务2.2.6：集成测试
- **描述**：端到端的集成测试
- **测试场景**：
  - 新用户开通试用流程
  - 现有用户状态查询
  - 试用和VIP并存场景
  - 数据一致性验证
- **预计时间**：2小时
- **测试工具**：Playwright自动化测试

## 3. 技术实现要点

### 3.1 数据库设计
```sql
-- 新增字段
ALTER TABLE inz_user_front 
ADD COLUMN trial_expiration_time DATETIME COMMENT '试用到期时间';

-- 添加索引（可选，根据查询需求）
CREATE INDEX idx_trial_expiration_time ON inz_user_front(trial_expiration_time);
```

### 3.2 实体类更新
```java
/**试用到期时间*/
@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
@ApiModelProperty(value = "试用到期时间")
private Date trialExpirationTime;
```

### 3.3 业务逻辑核心代码
```java
public enum UserStatus {
    TRIAL_ACTIVE, VIP_ACTIVE, BOTH_ACTIVE, EXPIRED, NORMAL
}

public UserStatus getUserStatus(InzUserFront user) {
    Date now = new Date();
    boolean trialValid = user.getTrialExpirationTime() != null && 
                        user.getTrialExpirationTime().after(now);
    boolean vipValid = user.getVipTime() != null && 
                      user.getVipTime().after(now);
    
    if (trialValid && vipValid) return UserStatus.BOTH_ACTIVE;
    if (trialValid) return UserStatus.TRIAL_ACTIVE;
    if (vipValid) return UserStatus.VIP_ACTIVE;
    if (user.getTrialExpirationTime() != null || user.getVipTime() != null) {
        return UserStatus.EXPIRED;
    }
    return UserStatus.NORMAL;
}
```

## 4. 数据迁移方案

### 4.1 迁移前分析
```sql
-- 分析现有数据
SELECT 
    COUNT(*) as total_users,
    COUNT(vip_time) as users_with_vip,
    COUNT(CASE WHEN vip_time > NOW() THEN 1 END) as active_vip_users
FROM inz_user_front;

-- 分析试用记录
SELECT 
    COUNT(DISTINCT user_id) as users_with_trial_records,
    MIN(create_time) as earliest_trial,
    MAX(create_time) as latest_trial
FROM inz_user_trial_log;
```

### 4.2 迁移策略
1. **保守策略**：新字段初始为NULL，只对新开通的试用设置
2. **积极策略**：尝试从试用记录推断现有的试用到期时间
3. **混合策略**：结合用户反馈和数据分析确定迁移范围

### 4.3 迁移脚本示例
```sql
-- 方案1：保守迁移（推荐）
-- 只为新字段添加默认值，不迁移现有数据
ALTER TABLE inz_user_front 
ADD COLUMN trial_expiration_time DATETIME DEFAULT NULL COMMENT '试用到期时间';

-- 方案2：基于试用记录的智能迁移
UPDATE inz_user_front u
SET trial_expiration_time = (
    SELECT DATE_ADD(MAX(t.create_time), INTERVAL 30 DAY)
    FROM inz_user_trial_log t 
    WHERE t.user_id = u.id 
    AND t.type = 0  -- 减少类型
    AND t.create_time >= DATE_SUB(NOW(), INTERVAL 60 DAY)
)
WHERE EXISTS (
    SELECT 1 FROM inz_user_trial_log t 
    WHERE t.user_id = u.id 
    AND t.create_time >= DATE_SUB(NOW(), INTERVAL 60 DAY)
);
```

## 5. 测试策略

### 5.1 单元测试
- **覆盖率目标**：90%以上
- **重点测试**：用户状态判断、时间设置、边界条件
- **工具**：JUnit + Mockito

### 5.2 集成测试
- **测试范围**：完整业务流程
- **重点场景**：新旧逻辑兼容、数据一致性
- **工具**：Playwright自动化测试

### 5.3 数据验证测试
- **验证内容**：迁移数据准确性、字段约束、索引性能
- **验证方法**：SQL查询验证、性能测试

## 6. 风险控制

### 6.1 技术风险
- **数据迁移风险**：通过分阶段迁移和充分测试控制
- **兼容性风险**：保持API向后兼容
- **性能风险**：通过索引优化和性能测试控制

### 6.2 业务风险
- **逻辑变更风险**：通过详细测试和灰度发布控制
- **用户体验风险**：确保功能平滑过渡

## 7. 交付物清单

### 7.1 数据库交付物
- [ ] 数据库变更脚本
- [ ] 数据迁移脚本
- [ ] 回滚脚本
- [ ] 索引优化脚本

### 7.2 代码交付物
- [ ] 更新的InzUserFront实体类
- [ ] 新增的UserStatus枚举
- [ ] 修改的业务逻辑代码
- [ ] 新增的API接口
- [ ] 完整的单元测试
- [ ] 集成测试套件

### 7.3 文档交付物
- [ ] 数据库设计文档
- [ ] API接口文档
- [ ] 数据迁移报告
- [ ] 测试报告

## 8. 时间安排

| 阶段 | 任务 | 负责人 | 预计时间 | 依赖关系 |
|------|------|--------|----------|----------|
| 1 | 数据库和架构设计 | Bob | 0.5天 | PRD完成 |
| 2 | 实体类和数据库层 | Alex | 0.5天 | 设计完成 |
| 3 | 业务逻辑实现 | Alex | 1天 | 数据库层完成 |
| 4 | API接口更新 | Alex | 0.5天 | 业务逻辑完成 |
| 5 | 测试和验证 | Alex | 0.5天 | 开发完成 |

## 9. 验收标准

### 9.1 功能验收
- [ ] 新字段正确添加到数据库
- [ ] 用户状态判断逻辑正确
- [ ] 试用时间设置不影响VIP时间
- [ ] API接口返回正确的状态信息

### 9.2 质量验收
- [ ] 单元测试覆盖率达到90%以上
- [ ] 所有集成测试通过
- [ ] 数据迁移验证通过
- [ ] 性能测试达标

### 9.3 兼容性验收
- [ ] 现有API接口保持兼容
- [ ] 现有业务逻辑不受影响
- [ ] 数据完整性得到保障
