# 架构设计 - trialRemainingDays数据一致性紧急修复

## 1. 问题诊断

### 1.1 问题现象
- 代理商为用户开通2天体验后，用户的剩余天数没有正确减少
- 日志显示操作成功，但实际数据不一致

### 1.2 根本原因分析

#### 1.2.1 数据流程问题
```mermaid
graph TD
    A[代理商开通体验] --> B[reduceTrialDays方法]
    B --> C[只记录到trial_log表]
    C --> D[updateUserTrialInfo方法]
    D --> E[重新计算剩余天数]
    E --> F[更新user_front表]
    
    G[calculateRemainingDays] --> H[从user_front.trial_remaining_days读取]
    H --> I[计算错误的基准值]
```

#### 1.2.2 关键缺陷
1. **数据源不一致**：`calculateRemainingDays` 从 `user_front.trial_remaining_days` 读取，而不是实时计算
2. **更新时机错误**：`updateUserTrialInfo` 在主事务外执行，可能失败
3. **计算逻辑错误**：没有在同一事务中原子性更新两个表

## 2. 修复架构设计

### 2.1 新的数据流程
```mermaid
sequenceDiagram
    participant C as Controller
    participant S as Service
    participant DB1 as trial_log表
    participant DB2 as user_front表
    
    C->>S: reduceTrialDays(userId, days)
    S->>S: 开始事务
    S->>DB2: SELECT FOR UPDATE user_front
    S->>S: 计算新的剩余天数
    S->>DB1: INSERT trial_log记录
    S->>DB2: UPDATE user_front.trial_remaining_days
    S->>S: 提交事务
    S->>C: 返回成功
```

### 2.2 核心修复点

#### 2.2.1 原子性事务设计
- 在同一事务中完成：
  1. 插入试用记录
  2. 更新用户剩余天数
  3. 数据一致性验证

#### 2.2.2 数据库锁机制
- 使用 `SELECT FOR UPDATE` 防止并发问题
- 确保数据读取和更新的原子性

#### 2.2.3 计算逻辑优化
- 直接基于当前数据库值计算
- 避免多次查询导致的不一致

## 3. 具体实现方案

### 3.1 修改 reduceTrialDays 方法

```java
@Override
@Transactional(rollbackFor = Exception.class)
public boolean reduceTrialDays(String userId, String operatorId, Integer days, String content) {
    try {
        // 1. 使用悲观锁获取用户信息
        InzUserFront user = inzUserFrontService.getByIdForUpdate(userId);
        if (user == null) {
            log.error("用户不存在 - 用户ID: {}", userId);
            return false;
        }
        
        // 2. 获取当前剩余天数（从数据库实时读取）
        Integer beforeDays = user.getTrialRemainingDays();
        if (beforeDays == null || beforeDays < days) {
            log.error("剩余天数不足 - 用户ID: {}, 当前剩余: {}, 需要: {}", 
                     userId, beforeDays, days);
            return false;
        }
        
        // 3. 计算新的剩余天数
        Integer afterDays = beforeDays - days;
        
        // 4. 创建试用记录
        InzUserTrialLog trialLog = new InzUserTrialLog();
        trialLog.setUserId(userId);
        trialLog.setOperatorId(operatorId);
        trialLog.setContent(content);
        trialLog.setTrialDays(-days);
        trialLog.setType(0);
        trialLog.setBeforeDays(beforeDays);
        trialLog.setAfterDays(afterDays);
        trialLog.setSourceType("agent_grant");
        trialLog.setCreateTime(new Date());
        
        // 5. 原子性更新：先插入记录，再更新用户表
        boolean logSuccess = this.save(trialLog);
        if (!logSuccess) {
            throw new RuntimeException("试用记录保存失败");
        }
        
        // 6. 更新用户剩余天数
        user.setTrialRemainingDays(afterDays);
        user.setTrialLastUpdate(new Date());
        boolean updateSuccess = inzUserFrontService.updateById(user);
        if (!updateSuccess) {
            throw new RuntimeException("用户信息更新失败");
        }
        
        log.info("试用天数减少成功 - 用户: {}, 减少: {}天, 前: {}, 后: {}, 操作者: {}", 
                userId, days, beforeDays, afterDays, operatorId);
        
        return true;
        
    } catch (Exception e) {
        log.error("减少试用天数失败 - 用户: {}, 天数: {}", userId, days, e);
        throw e; // 让事务回滚
    }
}
```

### 3.2 新增悲观锁查询方法

```java
// 在 IInzUserFrontService 中新增
InzUserFront getByIdForUpdate(String id);

// 在 InzUserFrontServiceImpl 中实现
@Override
public InzUserFront getByIdForUpdate(String id) {
    return baseMapper.selectByIdForUpdate(id);
}
```

### 3.3 数据库层支持

```xml
<!-- 在 InzUserFrontMapper.xml 中新增 -->
<select id="selectByIdForUpdate" resultType="org.jeecg.modules.user_front.entity.InzUserFront">
    SELECT * FROM inz_user_front WHERE id = #{id} FOR UPDATE
</select>
```

## 4. 数据一致性保障

### 4.1 事务边界控制
- 所有相关操作在同一个 `@Transactional` 方法中
- 使用 `rollbackFor = Exception.class` 确保异常回滚

### 4.2 并发控制
- 使用 `SELECT FOR UPDATE` 防止并发修改
- 避免脏读和幻读问题

### 4.3 数据验证
- 操作前验证剩余天数充足性
- 操作后验证数据更新成功
- 异常情况自动回滚

## 5. 性能优化考虑

### 5.1 锁粒度控制
- 只对必要的记录加锁
- 尽快释放锁资源

### 5.2 查询优化
- 减少不必要的数据库查询
- 合并相关操作

## 6. 测试验证方案

### 6.1 单元测试
- 正常减少天数场景
- 天数不足场景
- 并发操作场景
- 异常回滚场景

### 6.2 集成测试
- 端到端业务流程测试
- 数据一致性验证
- 性能压力测试

## 7. 部署和回滚计划

### 7.1 部署步骤
1. 备份当前数据
2. 部署新代码
3. 验证功能正常
4. 监控数据一致性

### 7.2 回滚预案
- 保留原有方法作为备份
- 快速切换机制
- 数据修复脚本

## 8. 监控和告警

### 8.1 关键指标
- 试用天数减少成功率
- 数据一致性检查
- 事务执行时间

### 8.2 异常告警
- 数据不一致告警
- 事务失败告警
- 性能异常告警
