# 数据库迁移方案 - 添加试用到期时间字段

## 1. 迁移概览

### 1.1 迁移目标
- 在 `inz_user_front` 表中新增 `trial_expiration_time` 字段
- 确保数据完整性和业务连续性
- 提供完整的回滚方案
- 最小化对现有业务的影响

### 1.2 迁移策略
**采用保守迁移策略**：
- 新字段初始值为NULL
- 不迁移现有数据，避免数据风险
- 只对新开通的试用设置新字段
- 保持现有业务逻辑完全兼容

## 2. 迁移前分析

### 2.1 现状分析脚本
```sql
-- 1. 分析用户总体情况
SELECT 
    COUNT(*) as total_users,
    COUNT(vip_time) as users_with_vip,
    COUNT(CASE WHEN vip_time > NOW() THEN 1 END) as active_vip_users,
    COUNT(CASE WHEN vip_time <= NOW() THEN 1 END) as expired_vip_users,
    COUNT(CASE WHEN trial_remaining_days > 0 THEN 1 END) as users_with_trial_days,
    COUNT(CASE WHEN trial_remaining_days = 0 THEN 1 END) as users_no_trial_days
FROM inz_user_front;

-- 2. 分析VIP时间分布
SELECT 
    CASE 
        WHEN vip_time IS NULL THEN '无VIP时间'
        WHEN vip_time > NOW() THEN '有效VIP'
        ELSE '过期VIP'
    END as vip_status,
    COUNT(*) as user_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM inz_user_front), 2) as percentage
FROM inz_user_front
GROUP BY 
    CASE 
        WHEN vip_time IS NULL THEN '无VIP时间'
        WHEN vip_time > NOW() THEN '有效VIP'
        ELSE '过期VIP'
    END;

-- 3. 分析试用天数分布
SELECT 
    CASE 
        WHEN trial_remaining_days IS NULL THEN '无试用天数'
        WHEN trial_remaining_days = 0 THEN '试用天数为0'
        WHEN trial_remaining_days BETWEEN 1 AND 10 THEN '1-10天'
        WHEN trial_remaining_days BETWEEN 11 AND 30 THEN '11-30天'
        ELSE '30天以上'
    END as trial_range,
    COUNT(*) as user_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM inz_user_front), 2) as percentage
FROM inz_user_front
GROUP BY 
    CASE 
        WHEN trial_remaining_days IS NULL THEN '无试用天数'
        WHEN trial_remaining_days = 0 THEN '试用天数为0'
        WHEN trial_remaining_days BETWEEN 1 AND 10 THEN '1-10天'
        WHEN trial_remaining_days BETWEEN 11 AND 30 THEN '11-30天'
        ELSE '30天以上'
    END;

-- 4. 分析试用记录情况
SELECT 
    COUNT(DISTINCT user_id) as users_with_trial_records,
    COUNT(*) as total_trial_records,
    MIN(create_time) as earliest_trial,
    MAX(create_time) as latest_trial,
    COUNT(CASE WHEN type = 0 THEN 1 END) as reduce_records,
    COUNT(CASE WHEN type = 1 THEN 1 END) as add_records
FROM inz_user_trial_log;

-- 5. 检查表结构
DESCRIBE inz_user_front;
SHOW INDEX FROM inz_user_front;
```

### 2.2 风险评估
| 风险类型 | 风险描述 | 影响程度 | 缓解措施 |
|----------|----------|----------|----------|
| 数据丢失 | DDL操作可能导致数据丢失 | 高 | 完整备份，分步执行 |
| 业务中断 | 迁移过程中服务不可用 | 中 | 选择低峰时段，快速执行 |
| 性能影响 | 新字段和索引影响查询性能 | 低 | 优化索引设计，监控性能 |
| 兼容性问题 | 现有代码不兼容新字段 | 低 | 保持向后兼容 |

## 3. 迁移执行计划

### 3.1 迁移时间窗口
- **建议时间**：凌晨2:00-4:00（业务低峰期）
- **预计耗时**：30分钟
- **回滚时间**：15分钟

### 3.2 迁移步骤

#### 步骤1：数据备份（5分钟）
```sql
-- 1. 备份整个表结构和数据
CREATE TABLE inz_user_front_backup_20250115 AS 
SELECT * FROM inz_user_front;

-- 2. 验证备份完整性
SELECT 
    (SELECT COUNT(*) FROM inz_user_front) as original_count,
    (SELECT COUNT(*) FROM inz_user_front_backup_20250115) as backup_count;

-- 3. 备份表结构
SHOW CREATE TABLE inz_user_front;
```

#### 步骤2：添加新字段（2分钟）
```sql
-- 1. 添加新字段
ALTER TABLE inz_user_front 
ADD COLUMN trial_expiration_time DATETIME DEFAULT NULL COMMENT '试用到期时间';

-- 2. 验证字段添加成功
DESCRIBE inz_user_front;

-- 3. 检查字段是否正确添加
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT, 
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'inz_user_front' 
AND COLUMN_NAME = 'trial_expiration_time';
```

#### 步骤3：创建索引（3分钟）
```sql
-- 1. 创建主要索引
CREATE INDEX idx_trial_expiration_time ON inz_user_front(trial_expiration_time);

-- 2. 创建复合索引用于状态查询
CREATE INDEX idx_user_status_query ON inz_user_front(trial_expiration_time, vip_time, status);

-- 3. 验证索引创建成功
SHOW INDEX FROM inz_user_front WHERE Key_name LIKE '%trial%';
```

#### 步骤4：数据验证（5分钟）
```sql
-- 1. 验证新字段默认值
SELECT 
    COUNT(*) as total_users,
    COUNT(trial_expiration_time) as users_with_trial_expiration,
    COUNT(CASE WHEN trial_expiration_time IS NULL THEN 1 END) as users_null_trial_expiration
FROM inz_user_front;

-- 2. 验证表结构完整性
SELECT 
    COUNT(*) as column_count
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'inz_user_front';

-- 3. 验证索引完整性
SELECT 
    COUNT(*) as index_count
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_NAME = 'inz_user_front';

-- 4. 性能测试查询
EXPLAIN SELECT * FROM inz_user_front 
WHERE trial_expiration_time > NOW() 
AND vip_time > NOW() 
AND status = 1;
```

#### 步骤5：应用重启和验证（10分钟）
```bash
# 1. 重启应用服务
systemctl restart your-application-service

# 2. 检查应用启动状态
systemctl status your-application-service

# 3. 检查应用日志
tail -f /path/to/application.log

# 4. 验证API接口
curl -X GET "http://localhost:8080/health"
```

#### 步骤6：功能验证（5分钟）
```sql
-- 1. 测试新字段的读写
UPDATE inz_user_front 
SET trial_expiration_time = DATE_ADD(NOW(), INTERVAL 7 DAY)
WHERE id = 'test_user_id';

SELECT id, trial_expiration_time, vip_time 
FROM inz_user_front 
WHERE id = 'test_user_id';

-- 2. 恢复测试数据
UPDATE inz_user_front 
SET trial_expiration_time = NULL
WHERE id = 'test_user_id';
```

## 4. 回滚方案

### 4.1 回滚触发条件
- 应用启动失败
- 关键功能异常
- 性能严重下降
- 数据完整性问题

### 4.2 回滚步骤

#### 快速回滚（5分钟）
```sql
-- 1. 停止应用服务
-- systemctl stop your-application-service

-- 2. 移除新增索引
DROP INDEX idx_trial_expiration_time ON inz_user_front;
DROP INDEX idx_user_status_query ON inz_user_front;

-- 3. 移除新增字段
ALTER TABLE inz_user_front DROP COLUMN trial_expiration_time;

-- 4. 验证回滚成功
DESCRIBE inz_user_front;
SHOW INDEX FROM inz_user_front;
```

#### 完整回滚（15分钟）
```sql
-- 1. 如果需要完整恢复数据
RENAME TABLE inz_user_front TO inz_user_front_failed;
RENAME TABLE inz_user_front_backup_20250115 TO inz_user_front;

-- 2. 验证数据完整性
SELECT COUNT(*) FROM inz_user_front;

-- 3. 重建必要的索引
-- 根据原始表结构重建索引
```

## 5. 监控和验证

### 5.1 迁移过程监控
```sql
-- 1. 监控表大小变化
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS table_size_mb
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name = 'inz_user_front';

-- 2. 监控索引使用情况
SELECT 
    table_name,
    index_name,
    cardinality
FROM information_schema.statistics 
WHERE table_name = 'inz_user_front'
ORDER BY cardinality DESC;
```

### 5.2 迁移后验证清单
- [ ] 新字段成功添加
- [ ] 索引创建成功
- [ ] 应用正常启动
- [ ] 关键API接口正常
- [ ] 数据库性能正常
- [ ] 备份数据完整
- [ ] 回滚方案可执行

## 6. 性能影响评估

### 6.1 预期性能影响
| 操作类型 | 影响程度 | 说明 |
|----------|----------|------|
| 查询性能 | 微小 | 新增字段对现有查询无影响 |
| 插入性能 | 微小 | 新增一个NULL字段，影响极小 |
| 更新性能 | 微小 | 只有涉及新字段的更新会有微小影响 |
| 存储空间 | 微小 | 每行增加8字节（DATETIME类型） |
| 索引空间 | 小 | 新增索引占用额外存储空间 |

### 6.2 性能监控脚本
```sql
-- 1. 查询性能测试
SET @start_time = NOW(6);
SELECT COUNT(*) FROM inz_user_front WHERE status = 1;
SELECT TIMESTAMPDIFF(MICROSECOND, @start_time, NOW(6)) as query_time_microseconds;

-- 2. 索引使用情况
EXPLAIN SELECT * FROM inz_user_front 
WHERE trial_expiration_time > NOW() 
AND status = 1;

-- 3. 表统计信息
ANALYZE TABLE inz_user_front;
SHOW TABLE STATUS LIKE 'inz_user_front';
```

## 7. 应急预案

### 7.1 常见问题处理

#### 问题1：字段添加失败
```sql
-- 检查表锁定情况
SHOW PROCESSLIST;

-- 检查表空间
SELECT 
    table_schema,
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS table_size_mb
FROM information_schema.tables 
WHERE table_name = 'inz_user_front';

-- 重试添加字段
ALTER TABLE inz_user_front 
ADD COLUMN trial_expiration_time DATETIME DEFAULT NULL COMMENT '试用到期时间';
```

#### 问题2：索引创建失败
```sql
-- 检查索引状态
SHOW INDEX FROM inz_user_front;

-- 重新创建索引
CREATE INDEX idx_trial_expiration_time ON inz_user_front(trial_expiration_time);
```

#### 问题3：应用启动失败
```bash
# 检查应用日志
tail -100 /path/to/application.log

# 检查数据库连接
mysql -u username -p -e "SELECT 1"

# 回滚到备份版本
# 执行回滚脚本
```

## 8. 迁移完成检查

### 8.1 最终验证脚本
```sql
-- 1. 完整性检查
SELECT 
    'inz_user_front' as table_name,
    COUNT(*) as total_records,
    COUNT(trial_expiration_time) as records_with_trial_expiration,
    COUNT(vip_time) as records_with_vip_time,
    COUNT(CASE WHEN trial_remaining_days > 0 THEN 1 END) as records_with_trial_days
FROM inz_user_front;

-- 2. 结构检查
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'inz_user_front'
AND COLUMN_NAME IN ('trial_expiration_time', 'vip_time', 'trial_remaining_days')
ORDER BY ORDINAL_POSITION;

-- 3. 索引检查
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    CARDINALITY
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_NAME = 'inz_user_front'
AND INDEX_NAME LIKE '%trial%'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;
```

### 8.2 迁移报告模板
```
数据库迁移报告
================

迁移时间：2025-01-15 02:00:00 - 02:30:00
执行人员：[姓名]
迁移版本：v1.0

迁移结果：
✅ 字段添加：成功
✅ 索引创建：成功  
✅ 数据验证：通过
✅ 应用启动：正常
✅ 功能测试：通过

性能影响：
- 查询性能：无明显影响
- 存储空间：增加约XXX MB
- 索引空间：增加约XXX MB

问题记录：
[如有问题，详细描述]

后续计划：
- 监控新字段使用情况
- 定期检查数据一致性
- 准备代码部署
```
