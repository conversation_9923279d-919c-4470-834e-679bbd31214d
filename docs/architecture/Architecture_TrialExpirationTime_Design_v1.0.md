# 架构设计 - 添加独立的试用到期时间字段

## 1. 架构概览

### 1.1 设计目标
- 添加独立的 `trial_expiration_time` 字段，与 `vip_time` 完全分离
- 简化业务逻辑，移除复杂的时间比较机制
- 提供清晰的用户状态判断体系
- 确保向后兼容性和数据完整性

### 1.2 核心架构原则
1. **单一职责**：试用时间和VIP时间各司其职
2. **数据一致性**：所有相关操作在事务中完成
3. **向后兼容**：现有API和业务逻辑保持兼容
4. **可扩展性**：为未来会员体系扩展提供基础

## 2. 数据库架构设计

### 2.1 表结构变更

#### 2.1.1 新增字段设计
```sql
-- 在 inz_user_front 表中新增字段
ALTER TABLE inz_user_front 
ADD COLUMN trial_expiration_time DATETIME DEFAULT NULL COMMENT '试用到期时间';

-- 添加索引以优化查询性能
CREATE INDEX idx_trial_expiration_time ON inz_user_front(trial_expiration_time);
CREATE INDEX idx_user_status_query ON inz_user_front(trial_expiration_time, vip_time, status);
```

#### 2.1.2 字段说明
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| trial_expiration_time | DATETIME | 试用到期时间 | 可空，默认NULL |
| vip_time | DATETIME | VIP到期时间 | 现有字段，保持不变 |
| trial_remaining_days | INT | 剩余可用试用天数 | 现有字段，保持不变 |

### 2.2 数据迁移策略

#### 2.2.1 迁移方案选择
**推荐方案：保守迁移**
- 新字段初始值为NULL
- 只对新开通的试用设置 `trial_expiration_time`
- 现有数据保持不变，避免数据风险

#### 2.2.2 迁移脚本
```sql
-- 第一阶段：添加字段
ALTER TABLE inz_user_front 
ADD COLUMN trial_expiration_time DATETIME DEFAULT NULL COMMENT '试用到期时间';

-- 第二阶段：数据验证查询
SELECT 
    COUNT(*) as total_users,
    COUNT(vip_time) as users_with_vip,
    COUNT(CASE WHEN vip_time > NOW() THEN 1 END) as active_vip_users,
    COUNT(CASE WHEN trial_remaining_days > 0 THEN 1 END) as users_with_trial_days
FROM inz_user_front;

-- 第三阶段：添加索引
CREATE INDEX idx_trial_expiration_time ON inz_user_front(trial_expiration_time);
CREATE INDEX idx_user_status_query ON inz_user_front(trial_expiration_time, vip_time, status);
```

## 3. 业务逻辑架构

### 3.1 用户状态枚举设计

```java
/**
 * 用户权限状态枚举
 */
public enum UserPermissionStatus {
    TRIAL_ACTIVE("trial_active", "试用中"),
    VIP_ACTIVE("vip_active", "VIP中"),
    BOTH_ACTIVE("both_active", "试用和VIP都有效"),
    EXPIRED("expired", "已过期"),
    NORMAL("normal", "普通用户");
    
    private final String code;
    private final String description;
    
    UserPermissionStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    // getter methods...
}
```

### 3.2 用户状态判断逻辑

```java
/**
 * 用户状态判断服务
 */
@Service
public class UserStatusService {
    
    /**
     * 获取用户权限状态
     */
    public UserPermissionStatus getUserPermissionStatus(InzUserFront user) {
        if (user == null) {
            return UserPermissionStatus.NORMAL;
        }
        
        Date now = new Date();
        boolean trialValid = isTrialValid(user, now);
        boolean vipValid = isVipValid(user, now);
        
        if (trialValid && vipValid) {
            return UserPermissionStatus.BOTH_ACTIVE;
        }
        if (trialValid) {
            return UserPermissionStatus.TRIAL_ACTIVE;
        }
        if (vipValid) {
            return UserPermissionStatus.VIP_ACTIVE;
        }
        if (hasExpiredPermissions(user)) {
            return UserPermissionStatus.EXPIRED;
        }
        return UserPermissionStatus.NORMAL;
    }
    
    /**
     * 检查试用是否有效
     */
    private boolean isTrialValid(InzUserFront user, Date now) {
        return user.getTrialExpirationTime() != null && 
               user.getTrialExpirationTime().after(now);
    }
    
    /**
     * 检查VIP是否有效
     */
    private boolean isVipValid(InzUserFront user, Date now) {
        return user.getVipTime() != null && 
               user.getVipTime().after(now);
    }
    
    /**
     * 检查是否有过期的权限
     */
    private boolean hasExpiredPermissions(InzUserFront user) {
        return user.getTrialExpirationTime() != null || 
               user.getVipTime() != null;
    }
    
    /**
     * 检查用户是否有有效权限
     */
    public boolean hasActivePermission(InzUserFront user) {
        UserPermissionStatus status = getUserPermissionStatus(user);
        return status == UserPermissionStatus.TRIAL_ACTIVE ||
               status == UserPermissionStatus.VIP_ACTIVE ||
               status == UserPermissionStatus.BOTH_ACTIVE;
    }
}
```

### 3.3 试用时间设置逻辑重构

```java
/**
 * 重构后的试用时间设置方法
 */
@Transactional(rollbackFor = Exception.class)
public void setTrialExpirationTime(String userId, Integer trialDays) {
    try {
        // 1. 获取用户信息
        InzUserFront user = inzUserFrontService.getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 2. 计算试用到期时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, trialDays);
        Date expirationTime = calendar.getTime();
        
        // 3. 直接设置试用到期时间，不与VIP时间比较
        user.setTrialExpirationTime(expirationTime);
        user.setTrialLastUpdate(new Date());
        
        // 4. 更新用户信息
        boolean updateSuccess = inzUserFrontService.updateById(user);
        if (!updateSuccess) {
            throw new BusinessException("试用时间设置失败");
        }
        
        log.info("试用到期时间设置成功 - 用户ID: {}, 试用天数: {}天, 到期时间: {}",
                userId, trialDays, expirationTime);
                
    } catch (Exception e) {
        log.error("设置试用到期时间失败 - 用户ID: {}, 试用天数: {}", userId, trialDays, e);
        throw e;
    }
}
```

## 4. API接口架构

### 4.1 用户状态查询接口

```java
/**
 * 用户状态查询控制器
 */
@RestController
@RequestMapping("/api/user/status")
@Api(tags = "用户状态管理")
public class UserStatusController {
    
    @Autowired
    private UserStatusService userStatusService;
    
    @Autowired
    private IInzUserFrontService userFrontService;
    
    /**
     * 获取用户详细状态信息
     */
    @GetMapping("/detail")
    @ApiOperation("获取用户详细状态信息")
    public Result<UserStatusDetailVO> getUserStatusDetail(
            @RequestParam String userId) {
        
        InzUserFront user = userFrontService.getById(userId);
        if (user == null) {
            return Result.error("用户不存在");
        }
        
        UserStatusDetailVO statusVO = buildUserStatusDetail(user);
        return Result.OK(statusVO);
    }
    
    /**
     * 批量获取用户状态
     */
    @PostMapping("/batch")
    @ApiOperation("批量获取用户状态")
    public Result<List<UserStatusVO>> getBatchUserStatus(
            @RequestBody List<String> userIds) {
        
        List<UserStatusVO> statusList = userIds.stream()
            .map(this::getUserStatus)
            .collect(Collectors.toList());
            
        return Result.OK(statusList);
    }
    
    private UserStatusDetailVO buildUserStatusDetail(InzUserFront user) {
        UserStatusDetailVO vo = new UserStatusDetailVO();
        vo.setUserId(user.getId());
        vo.setRealName(user.getRealName());
        vo.setPhone(user.getPhone());
        vo.setTrialExpirationTime(user.getTrialExpirationTime());
        vo.setVipTime(user.getVipTime());
        vo.setTrialRemainingDays(user.getTrialRemainingDays());
        vo.setPermissionStatus(userStatusService.getUserPermissionStatus(user));
        vo.setHasActivePermission(userStatusService.hasActivePermission(user));
        vo.setStatusDescription(getStatusDescription(vo.getPermissionStatus()));
        return vo;
    }
}
```

### 4.2 响应数据结构

```java
/**
 * 用户状态详细信息VO
 */
@Data
@ApiModel("用户状态详细信息")
public class UserStatusDetailVO {
    
    @ApiModelProperty("用户ID")
    private String userId;
    
    @ApiModelProperty("用户姓名")
    private String realName;
    
    @ApiModelProperty("手机号")
    private String phone;
    
    @ApiModelProperty("试用到期时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date trialExpirationTime;
    
    @ApiModelProperty("VIP到期时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date vipTime;
    
    @ApiModelProperty("剩余试用天数")
    private Integer trialRemainingDays;
    
    @ApiModelProperty("权限状态")
    private UserPermissionStatus permissionStatus;
    
    @ApiModelProperty("是否有有效权限")
    private Boolean hasActivePermission;
    
    @ApiModelProperty("状态描述")
    private String statusDescription;
    
    @ApiModelProperty("试用剩余小时数")
    private Long trialRemainingHours;
    
    @ApiModelProperty("VIP剩余天数")
    private Long vipRemainingDays;
}

/**
 * 用户状态简要信息VO
 */
@Data
@ApiModel("用户状态简要信息")
public class UserStatusVO {
    
    @ApiModelProperty("用户ID")
    private String userId;
    
    @ApiModelProperty("权限状态")
    private UserPermissionStatus permissionStatus;
    
    @ApiModelProperty("是否有有效权限")
    private Boolean hasActivePermission;
    
    @ApiModelProperty("状态描述")
    private String statusDescription;
}
```

## 5. 实体类架构更新

### 5.1 InzUserFront实体类更新

```java
/**
 * 在InzUserFront类中新增字段
 */
public class InzUserFront implements Serializable {
    
    // ... 现有字段 ...
    
    /**试用到期时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "试用到期时间")
    private Date trialExpirationTime;
    
    // getter and setter methods
    public Date getTrialExpirationTime() {
        return trialExpirationTime;
    }
    
    public void setTrialExpirationTime(Date trialExpirationTime) {
        this.trialExpirationTime = trialExpirationTime;
    }
}
```

## 6. 服务层架构

### 6.1 服务接口扩展

```java
/**
 * 在IInzUserFrontService中新增方法
 */
public interface IInzUserFrontService extends IService<InzUserFront> {
    
    // ... 现有方法 ...
    
    /**
     * 设置用户试用到期时间
     */
    void setTrialExpirationTime(String userId, Integer trialDays);
    
    /**
     * 获取用户权限状态
     */
    UserPermissionStatus getUserPermissionStatus(String userId);
    
    /**
     * 检查用户是否有有效权限
     */
    boolean hasActivePermission(String userId);
    
    /**
     * 获取用户状态详细信息
     */
    UserStatusDetailVO getUserStatusDetail(String userId);
}
```

## 7. 性能优化架构

### 7.1 索引策略
```sql
-- 主要查询索引
CREATE INDEX idx_trial_expiration_time ON inz_user_front(trial_expiration_time);
CREATE INDEX idx_vip_time ON inz_user_front(vip_time);

-- 复合索引用于状态查询
CREATE INDEX idx_user_status_query ON inz_user_front(trial_expiration_time, vip_time, status);

-- 用户查询优化索引
CREATE INDEX idx_user_phone_status ON inz_user_front(phone, status);
```

### 7.2 缓存策略
```java
/**
 * 用户状态缓存服务
 */
@Service
public class UserStatusCacheService {
    
    @Cacheable(value = "userStatus", key = "#userId", unless = "#result == null")
    public UserStatusDetailVO getUserStatusWithCache(String userId) {
        // 实现缓存逻辑
        return getUserStatusDetail(userId);
    }
    
    @CacheEvict(value = "userStatus", key = "#userId")
    public void evictUserStatusCache(String userId) {
        // 清除缓存
    }
}
```

## 8. 监控和告警架构

### 8.1 关键指标监控
- 试用到期时间设置成功率
- 用户状态查询响应时间
- 数据一致性检查
- API接口调用量和错误率

### 8.2 数据一致性检查
```sql
-- 定期执行的数据一致性检查
SELECT 
    COUNT(*) as inconsistent_users
FROM inz_user_front 
WHERE (trial_expiration_time IS NOT NULL AND trial_expiration_time < NOW() AND trial_remaining_days > 0)
   OR (trial_expiration_time IS NULL AND trial_remaining_days > 0);
```

## 9. 部署架构

### 9.1 分阶段部署策略
1. **阶段1**: 数据库字段添加和索引创建
2. **阶段2**: 后端代码部署和API更新
3. **阶段3**: 前端界面更新（如需要）
4. **阶段4**: 数据验证和监控启用

### 9.2 回滚方案
```sql
-- 紧急回滚脚本
-- 1. 移除新增字段
ALTER TABLE inz_user_front DROP COLUMN trial_expiration_time;

-- 2. 移除相关索引
DROP INDEX idx_trial_expiration_time ON inz_user_front;
DROP INDEX idx_user_status_query ON inz_user_front;
```

## 10. 安全架构考虑

### 10.1 权限控制
- 用户状态查询需要适当的权限验证
- 试用时间设置只能由授权的代理商执行
- 敏感操作需要审计日志记录

### 10.2 数据保护
- 用户状态信息的访问控制
- 个人信息的脱敏处理
- 操作日志的完整记录
