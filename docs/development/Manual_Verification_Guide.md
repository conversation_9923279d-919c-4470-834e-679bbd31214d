# trialRemainingDays修复效果手动验证指南

## 🎯 验证目标
确认代理商为用户开通体验后，用户的 `trial_remaining_days` 能够正确减少，并且数据保持一致性。

## 📋 验证步骤

### 步骤1: 准备测试数据

#### 1.1 查找或创建测试用户
```sql
-- 查找现有用户
SELECT id, real_name, phone, trial_remaining_days, trial_last_update 
FROM inz_user_front 
WHERE trial_remaining_days > 5 
LIMIT 1;

-- 或者更新一个测试用户的剩余天数
UPDATE inz_user_front 
SET trial_remaining_days = 20, trial_last_update = NOW() 
WHERE id = 'YOUR_TEST_USER_ID';
```

#### 1.2 记录初始状态
```sql
-- 记录用户当前状态
SELECT 
    id,
    real_name,
    trial_remaining_days as current_remaining_days,
    trial_last_update
FROM inz_user_front 
WHERE id = 'YOUR_TEST_USER_ID';

-- 查看该用户的历史试用记录
SELECT 
    id,
    trial_days,
    before_days,
    after_days,
    content,
    create_time
FROM inz_user_trial_log 
WHERE user_id = 'YOUR_TEST_USER_ID'
ORDER BY create_time DESC;
```

### 步骤2: 执行开通体验操作

#### 2.1 通过API接口测试
使用Postman或其他API测试工具：

**请求URL**: `POST /sys/permissionGrant/grantTrial`

**请求Headers**:
```
Content-Type: application/json
X-Access-Token: YOUR_ACCESS_TOKEN
```

**请求Body**:
```json
{
    "userId": "YOUR_TEST_USER_ID",
    "educationId": "1878987782752366594",
    "duration": 3
}
```

#### 2.2 预期响应
```json
{
    "success": true,
    "message": "试用权限开通成功",
    "code": 200,
    "result": "试用权限开通成功",
    "timestamp": 1705123456789
}
```

### 步骤3: 验证数据一致性

#### 3.1 检查用户表数据
```sql
-- 验证用户剩余天数是否正确减少
SELECT 
    id,
    real_name,
    trial_remaining_days as updated_remaining_days,
    trial_last_update
FROM inz_user_front 
WHERE id = 'YOUR_TEST_USER_ID';

-- 预期结果：trial_remaining_days 应该减少了3天
-- 例如：原来20天，现在应该是17天
```

#### 3.2 检查试用记录表
```sql
-- 验证是否生成了正确的试用记录
SELECT 
    id,
    user_id,
    operator_id,
    trial_days,
    type,
    before_days,
    after_days,
    content,
    source_type,
    create_time
FROM inz_user_trial_log 
WHERE user_id = 'YOUR_TEST_USER_ID'
ORDER BY create_time DESC
LIMIT 1;

-- 预期结果验证：
-- trial_days = -3 (负数表示减少)
-- type = 0 (0表示减少)
-- before_days = 20 (操作前天数)
-- after_days = 17 (操作后天数)
-- source_type = 'agent_grant' (代理商操作)
-- content 包含教育系列信息
```

### 步骤4: 验证边界情况

#### 4.1 测试天数不足场景
```json
{
    "userId": "YOUR_TEST_USER_ID",
    "educationId": "1878987782752366594",
    "duration": 25
}
```

**预期结果**:
- API返回错误：`"配置天数不能超过剩余可用天数(17天)"`
- 用户表数据不变
- 不生成新的试用记录

#### 4.2 测试用户不存在场景
```json
{
    "userId": "NON_EXISTENT_USER_ID",
    "educationId": "1878987782752366594",
    "duration": 3
}
```

**预期结果**:
- API返回错误：`"该用户不在您的管辖范围内"` 或其他用户不存在的错误
- 不生成任何记录

## ✅ 验证检查清单

### 数据一致性检查
- [ ] 用户表中的 `trial_remaining_days` 正确减少
- [ ] 用户表中的 `trial_last_update` 已更新
- [ ] 试用记录表中生成了正确的记录
- [ ] 记录中的 `before_days` 和 `after_days` 数据正确
- [ ] 记录中的 `trial_days` 为负数（减少操作）
- [ ] 记录中的 `source_type` 为 'agent_grant'

### 业务逻辑检查
- [ ] 天数充足时操作成功
- [ ] 天数不足时操作失败且数据不变
- [ ] 用户不存在时操作失败
- [ ] 权限验证正常工作

### 日志检查
查看应用日志，确认：
- [ ] 成功操作有正确的INFO日志
- [ ] 失败操作有正确的ERROR日志
- [ ] 日志中包含操作前后的天数信息

## 🚨 问题排查

### 如果剩余天数没有减少
1. 检查应用日志中是否有异常信息
2. 确认事务是否正确提交
3. 检查数据库连接和权限
4. 验证代码是否正确部署

### 如果出现数据不一致
1. 检查是否有并发操作
2. 确认事务隔离级别设置
3. 查看是否有其他地方修改了数据

### 常见错误信息
- `"剩余天数不足"`: 用户可用天数不够
- `"用户不存在"`: 用户ID无效
- `"该用户不在您的管辖范围内"`: 权限验证失败
- `"试用记录保存失败"`: 数据库操作异常
- `"用户信息更新失败"`: 用户表更新异常

## 📊 验证报告模板

```
验证时间：2025-01-15 XX:XX:XX
验证人员：[姓名]
测试用户ID：[USER_ID]

验证结果：
✅ 正常减少天数场景：通过/失败
✅ 天数不足场景：通过/失败  
✅ 数据一致性：通过/失败
✅ 日志记录：通过/失败

详细说明：
[具体的验证过程和结果]

问题记录：
[如有问题，详细描述]
```

## 🔄 回滚方案

如果验证发现问题，可以：

1. **代码回滚**: 恢复到修复前的版本
2. **数据修复**: 使用备份数据恢复
3. **手动修复**: 针对特定数据进行手动调整

```sql
-- 紧急数据修复示例
UPDATE inz_user_front 
SET trial_remaining_days = [正确的值], 
    trial_last_update = NOW() 
WHERE id = 'AFFECTED_USER_ID';
```
