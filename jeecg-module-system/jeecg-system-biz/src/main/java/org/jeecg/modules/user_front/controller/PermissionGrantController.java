package org.jeecg.modules.user_front.controller;

import com.alipay.api.domain.EduAgeDemand;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.modules.config.entity.InzConfig;
import org.jeecg.modules.config.service.IInzConfigService;
import org.jeecg.modules.education.entity.InzEducation;
import org.jeecg.modules.education.service.IInzEducationService;
import org.jeecg.modules.user_front.entity.InzUserFront;
import org.jeecg.modules.user_front.entity.InzUserPayLog;
import org.jeecg.modules.user_front.entity.InzUserTrialLog;
import org.jeecg.modules.user_front.service.IInzUserFrontService;
import org.jeecg.modules.user_front.service.IInzUserPayLogService;
import org.jeecg.modules.user_front.service.IInzUserTrialLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
// 在文件顶部添加验证注解的import
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.user_front.entity.InzUserTrialLog;
import org.jeecg.modules.user_front.entity.InzUserPayLog;
import org.jeecg.modules.user_front.service.IInzUserPayLogService;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @Description: 后台权限开通管理Controller
 * @Author: Alex (工程师)
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Api(tags = "后台管理 - 权限开通管理")
@RestController
@RequestMapping("/user_front/permission")
@Slf4j
public class PermissionGrantController extends JeecgController<InzUserFront, IInzUserFrontService> {

    @Autowired
    private IInzUserFrontService inzUserFrontService;

    @Autowired
    private IInzConfigService inzConfigService;

    @Autowired
    private IInzEducationService inzEducationService;

    /**
     * 获取指定用户的剩余试用天数
     */
    @AutoLog(value = "获取用户剩余试用天数")
    @ApiOperation(value = "获取用户剩余试用天数", notes = "获取指定前台用户的剩余可配置试用天数")
    @GetMapping(value = "/getUserRemainingDays")
    public Result<Integer> getUserRemainingDays(
            @ApiParam(value = "前台用户ID", required = true) @RequestParam("userId") String userId,
            HttpServletRequest req) {
        try {
            String operatorId = CommonUtils.getUserIdByToken();
            log.debug("获取用户剩余试用天数 - 操作者: {}, 目标用户: {}", operatorId, userId);

            // 验证权限：检查目标用户是否在操作者的管辖范围内
            if (!validateUserManagementPermission(operatorId, userId)) {
                return Result.error("该用户不在您的管辖范围内");
            }

            LambdaQueryWrapper<InzUserFront> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(InzUserFront::getId, userId);
            InzUserFront userFront = inzUserFrontService.getOne(queryWrapper);

            // 获取用户剩余试用天数
            Integer remainingDays = userFront.getTrialRemainingDays();
            log.info("用户剩余试用天数查询成功 - 用户: {}, 剩余天数: {}", userId, remainingDays);
            return Result.OK(remainingDays);
        } catch (Exception e) {
            log.error("获取用户剩余试用天数失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 开通试用权限
     */
    @AutoLog(value = "开通试用权限")
    @ApiOperation(value = "开通试用权限", notes = "代理商为用户开通试用权限")
    @PostMapping(value = "/grantTrial")
    public Result<String> grantTrial(@Valid @RequestBody PermissionGrantRequest request, HttpServletRequest req) {
        try {
            String operatorId = CommonUtils.getUserIdByToken();
            log.info("开通试用权限 - 操作者: {}, 目标用户: {}, 天数: {}",
                    operatorId, request.getUserId(), request.getDuration());

            // 验证权限
            if (!validateUserManagementPermission(operatorId, request.getUserId())) {
                return Result.error("该用户不在您的管辖范围内");
            }

            LambdaQueryWrapper<InzUserFront> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(InzUserFront::getId, request.getUserId());
            InzUserFront userFront = inzUserFrontService.getOne(queryWrapper);
            // 验证试用天数
            Integer remainingDays = userFront.getTrialRemainingDays();
            if (request.getDuration() > remainingDays) {
                return Result.error("配置天数不能超过剩余可用天数(" + remainingDays + "天)");
            }

            // 执行开通逻辑
            boolean success = grantTrialPermission(request);

            if (success) {
                log.info("试用权限开通成功 - 操作者: {}, 目标用户: {}, 天数: {}",
                        operatorId, request.getUserId(), request.getDuration());
                return Result.OK("试用权限开通成功");
            } else {
                return Result.error("试用权限开通失败");
            }
        } catch (Exception e) {
            log.error("开通试用权限失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 开通全年权限
     */
    @AutoLog(value = "开通全年权限")
    @ApiOperation(value = "开通全年权限", notes = "代理商为用户开通全年权限")
    @PostMapping(value = "/grantAnnual")
    public Result<String> grantAnnual(@Valid @RequestBody PermissionGrantRequest request, HttpServletRequest req) {
        try {
            String operatorId = CommonUtils.getUserIdByToken();
            log.info("开通全年权限 - 操作者: {}, 目标用户: {}", operatorId, request.getUserId());

            // 验证权限
            if (!validateUserManagementPermission(operatorId, request.getUserId())) {
                return Result.error("该用户不在您的管辖范围内");
            }

            // 执行开通逻辑
            boolean success = grantAnnualPermission(request);

            if (success) {
                log.info("全年权限开通成功 - 操作者: {}, 目标用户: {}", operatorId, request.getUserId());
                LambdaQueryWrapper<InzUserFront> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(InzUserFront::getId, request.getUserId());
                InzUserFront userFront = inzUserFrontService.getOne(queryWrapper);
                userFront.setFormalUser("1");
                inzUserFrontService.updateById(userFront);
                return Result.OK("全年权限开通成功");
            } else {
                return Result.error("全年权限开通失败");
            }
        } catch (Exception e) {
            log.error("开通全年权限失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取权限开通历史
     */
    @AutoLog(value = "获取权限开通历史")
    @ApiOperation(value = "获取权限开通历史", notes = "获取指定用户的权限开通历史记录")
    @GetMapping(value = "/getGrantHistory")
    public Result<IPage<PermissionGrantHistoryVO>> getGrantHistory(
            @ApiParam(value = "前台用户ID", required = true) @RequestParam("userId") String userId,
            @ApiParam(value = "页码", required = false) @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @ApiParam(value = "页大小", required = false) @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            HttpServletRequest req) {
        try {
            String operatorId = CommonUtils.getUserIdByToken();
            log.debug("获取权限开通历史 - 操作者: {}, 目标用户: {}", operatorId, userId);

            // 验证权限
            if (!validateUserManagementPermission(operatorId, userId)) {
                return Result.error("该用户不在您的管辖范围内");
            }

            // 实现权限开通历史查询逻辑
            log.info("查询权限开通历史 - 用户: {}, 页码: {}, 页大小: {}", userId, pageNo, pageSize);

            // 创建分页对象
            Page<PermissionGrantHistoryVO> page = new Page<>(pageNo, pageSize);

            // 查询试用记录历史
            List<InzUserTrialLog> trialLogs = inzUserTrialLogService.selectByUserId(userId);

            // 转换为历史记录VO
            List<PermissionGrantHistoryVO> historyList = trialLogs.stream()
                .map(this::convertToHistoryVO)
                .collect(java.util.stream.Collectors.toList());

            // 手动分页（因为是从多个表查询合并的数据）
            int start = (pageNo - 1) * pageSize;
            int end = Math.min(start + pageSize, historyList.size());
            List<PermissionGrantHistoryVO> pageData = historyList.subList(start, end);

            // 设置分页结果
            page.setRecords(pageData);
            page.setTotal(historyList.size());
            page.setCurrent(pageNo);
            page.setSize(pageSize);

            return Result.OK(page);
        } catch (Exception e) {
            log.error("获取权限开通历史失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 验证用户管理权限
     * 将后台用户ID转换为前台用户ID，然后验证管辖范围
     */
    private boolean validateUserManagementPermission(String backendUserId, String frontendUserId) {
        try {
            // 1. 根据后台用户ID查找对应的前台用户
            InzUserFront frontendAgent = inzUserFrontService.getByBackendUserId(backendUserId);

            if (frontendAgent == null) {
                log.warn("后台用户没有对应的前台用户 - 后台用户ID: {}", backendUserId);
                return false;
            }

            // 2. 使用前台用户ID验证管辖范围
            String frontendAgentId = frontendAgent.getId();
            boolean hasPermission = isUserInManagementScope(frontendAgentId, frontendUserId);

            log.debug("权限验证结果 - 后台用户: {}, 前台代理商: {}, 目标用户: {}, 有权限: {}",
                     backendUserId, frontendAgentId, frontendUserId, hasPermission);

            return hasPermission;
        } catch (Exception e) {
            log.error("验证用户管理权限失败 - 后台用户: {}, 目标用户: {}", backendUserId, frontendUserId, e);
            return false;
        }
    }

    /**
     * 检查用户是否在管辖范围内
     */
    private boolean isUserInManagementScope(String agentId, String userId) {
        return checkUserHierarchy(agentId, userId);
    }

    /**
     * 检查用户层级关系
     */
    private boolean checkUserHierarchy(String agentId, String userId) {
        try {
            InzUserFront user = inzUserFrontService.getById(userId);
            if (user == null) {
                return false;
            }

            // 检查直接上级关系
            if (agentId.equals(user.getParentId())) {
                return true;
            }

            // 检查间接上级关系（递归查找）
            String currentParentId = user.getParentId();
            int maxDepth = 10; // 防止无限递归
            int depth = 0;

            while (currentParentId != null && depth < maxDepth) {
                if (agentId.equals(currentParentId)) {
                    return true;
                }

                InzUserFront parent = inzUserFrontService.getById(currentParentId);
                if (parent == null) {
                    break;
                }

                currentParentId = parent.getParentId();
                depth++;
            }

            return false;
        } catch (Exception e) {
            log.error("检查用户层级关系失败 - 代理商: {}, 用户: {}", agentId, userId, e);
            return false;
        }
    }

    /**
     * 开通试用权限的具体实现
     */
    @Autowired
    private IInzUserTrialLogService inzUserTrialLogService;

    @Autowired
    private IInzUserPayLogService inzUserPayLogService;


    private boolean grantTrialPermission(PermissionGrantRequest request) {
        try {
            // 1. 获取当前登录用户作为操作者
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String operatorId = loginUser.getId();

            // 2. 验证用户是否存在
            InzUserFront user = inzUserFrontService.getById(request.getUserId());
            if (user == null) {
                log.error("用户不存在 - 用户ID: {}", request.getUserId());
                return false;
            }

            LambdaQueryWrapper<InzUserFront> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(InzUserFront::getId, request.getUserId());
            InzUserFront userFront = inzUserFrontService.getOne(queryWrapper);

            // 3. 检查用户剩余试用天数
            Integer remainingDays = userFront.getTrialRemainingDays();
            if (remainingDays <= 0) {
                log.warn("用户试用天数已用完 - 用户ID: {}, 剩余天数: {}", request.getUserId(), remainingDays);
                return false;
            }

            // 4. 检查请求的天数是否超过剩余天数
            if (request.getDuration() > remainingDays) {
                log.warn("请求天数超过剩余试用天数 - 用户ID: {}, 请求: {}天, 剩余: {}天",
                        request.getUserId(), request.getDuration(), remainingDays);
                return false;
            }

            // 5. 记录试用天数的消耗
            String content = String.format("开通教育系列[%s]试用权限，消耗%d天试用时间",
                                         request.getEducationId(), request.getDuration());
            boolean logSuccess = inzUserTrialLogService.reduceTrialDays(
                request.getUserId(),
                operatorId,
                request.getDuration(),
                content
            );

            if (!logSuccess) {
                log.error("添加试用记录失败 - 用户ID: {}", request.getUserId());
                return false;
            }

            // 6. 设置试用到期时间
            setTrialExpirationTime(request.getUserId(), request.getDuration());

            log.info("试用权限开通成功 - 用户ID: {}, 教育系列: {}, 天数: {}",
                    request.getUserId(), request.getEducationId(), request.getDuration());

            return true;

        } catch (Exception e) {
            log.error("开通试用权限失败 - 用户ID: {}", request.getUserId(), e);
            return false;
        }
    }

    /**
     * 设置试用到期时间
     */
    private void setTrialExpirationTime(String userId, Integer trialDays) {
        try {
            // 使用新的服务方法设置试用到期时间
            inzUserFrontService.setTrialExpirationTime(userId, trialDays);
            log.info("试用到期时间设置成功 - 用户ID: {}, 试用天数: {}天", userId, trialDays);
        } catch (Exception e) {
            log.error("设置试用到期时间失败 - 用户ID: {}, 试用天数: {}", userId, trialDays, e);
        }
    }


    /**
     * 开通全年权限的具体实现
     */
    private boolean grantAnnualPermission(PermissionGrantRequest request) {
        try {
            // 1. 获取当前登录用户作为操作者
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String operatorId = loginUser.getId();

            // 2. 验证用户是否存在
            InzUserFront user = inzUserFrontService.getById(request.getUserId());
            if (user == null) {
                log.error("用户不存在 - 用户ID: {}", request.getUserId());
                return false;
            }

            // 3. 检查操作者的金豆是否足够（假设年度权限需要消耗金豆）
            InzUserFront operator = inzUserFrontService.getById(operatorId);
            if (operator == null || operator.getGoldenBean() < getAnnualPermissionCost(request.getEducationId())) {
                log.warn("操作者金豆不足 - 操作者ID: {}, 当前金豆: {}, 需要: {}",
                        operatorId, operator != null ? operator.getGoldenBean() : 0, getAnnualPermissionCost(request.getEducationId()));
                return false;
            }

            // 4. 扣除金豆
            operator.setGoldenBean(operator.getGoldenBean() - getAnnualPermissionCost(request.getEducationId()));
            inzUserFrontService.updateById(operator);

            // 5. 记录金豆消费日志
            recordGoldenBeanConsumption(operatorId, request.getUserId(), request.getEducationId());

            // 6. 设置用户年度权限
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.YEAR, 1);
            user.setVipTime(calendar.getTime());
            inzUserFrontService.updateById(user);

            // 7. 记录操作日志
            String content = String.format("开通教育系列[%s]年度权限，有效期至%s",
                                         request.getEducationId(),
                                         new SimpleDateFormat("yyyy-MM-dd").format(calendar.getTime()));

            log.info("年度权限开通成功 - 用户ID: {}, 教育系列: {}, 有效期至: {}",
                    request.getUserId(), request.getEducationId(), calendar.getTime());

            return true;

        } catch (Exception e) {
            log.error("开通年度权限失败 - 用户ID: {}", request.getUserId(), e);
            return false;
        }
    }

    /**
     * 获取年度权限开通所需的金豆数量
     */
    private Integer getAnnualPermissionCost(String educationId) {
        try {
            if (educationId == null || educationId.trim().isEmpty()) {
                log.warn("教育系列ID为空，使用默认费用100金豆");
                return 100;
            }

            // 从教育系列表中获取年度权限费用
            LambdaQueryWrapper<InzEducation> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(InzEducation::getId, educationId.trim());
            InzEducation education = inzEducationService.getOne(wrapper);

            if (education == null) {
                log.warn("教育系列不存在 - ID: {}, 使用默认费用100金豆", educationId);
                return 100;
            }

            Integer cost = education.getGoldenBean();
            if (cost == null || cost < 0) {
                log.warn("教育系列金豆费用配置异常 - ID: {}, 费用: {}, 使用默认费用100金豆", educationId, cost);
                return 100;
            }

            log.debug("获取年度权限费用成功 - 教育系列: {}, 费用: {}金豆", educationId, cost);
            return cost;
        } catch (Exception e) {
            log.error("获取年度权限费用失败 - 教育系列ID: {}, 使用默认费用100金豆", educationId, e);
            return 100;
        }
    }

    /**
     * 转换试用记录为权限开通历史VO
     */
    private PermissionGrantHistoryVO convertToHistoryVO(InzUserTrialLog trialLog) {
        PermissionGrantHistoryVO vo = new PermissionGrantHistoryVO();
        vo.setId(trialLog.getId());
        vo.setUserId(trialLog.getUserId());
        vo.setOperatorId(trialLog.getOperatorId());
        vo.setOperationType(trialLog.getType() == 1 ? "试用权限开通" : "试用权限扣减");
        vo.setDescription(trialLog.getContent());
        vo.setDuration(trialLog.getTrialDays());
        vo.setEducationId(trialLog.getEducationSeriesId());
        vo.setBeforeDays(trialLog.getBeforeDays());
        vo.setAfterDays(trialLog.getAfterDays());
        vo.setCreateTime(trialLog.getCreateTime());
        vo.setSourceType(trialLog.getSourceType());
        return vo;
    }

    /**
     * 记录金豆消费日志
     */
    private void recordGoldenBeanConsumption(String operatorId, String userId, String educationId) {
        try {
            log.info("记录金豆消费 - 操作者: {}, 目标用户: {}, 教育系列: {}, 消费金豆: {}",
                    operatorId, userId, educationId, getAnnualPermissionCost(educationId));

            // 实现具体的金豆消费记录逻辑
            InzUserPayLog payLog = new InzUserPayLog();
            payLog.setUserId(operatorId); // 操作者消费金豆
            payLog.setSourceUserId(userId); // 目标用户
            LambdaQueryWrapper<InzEducation> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(InzEducation::getId, educationId);
            InzEducation education = inzEducationService.getOne(wrapper);
            String educationName = (education != null && education.getName() != null) ? education.getName() : educationId;
            payLog.setContent(String.format("年度权限开通 - 为用户[%s]开通教育系列[%s]年度权限", userId, educationName));
            payLog.setGoldenBean(getAnnualPermissionCost(educationId));
            payLog.setType(0); // 0表示减少
            payLog.setCreateTime(new Date());

            // 获取操作者当前金豆余额
            InzUserFront operator = inzUserFrontService.getById(operatorId);
            if (operator != null) {
                payLog.setBeforeBalance(operator.getGoldenBean());
                payLog.setAfterBalance(operator.getGoldenBean() - getAnnualPermissionCost(educationId));
            }

            // 保存金豆消费记录
            boolean saveSuccess = inzUserPayLogService.save(payLog);
            if (saveSuccess) {
                log.info("金豆消费记录保存成功 - 记录ID: {}", payLog.getId());
            } else {
                log.error("金豆消费记录保存失败 - 操作者: {}, 目标用户: {}", operatorId, userId);
            }

        } catch (Exception e) {
            log.error("记录金豆消费失败 - 操作者: {}, 目标用户: {}", operatorId, userId, e);
        }
    }

    /**
     * 权限开通请求DTO
     */
    @Data
    public static class PermissionGrantRequest {
        @NotNull(message = "用户ID不能为空")
        private String userId;

        @NotNull(message = "教育系列ID不能为空")
        private String educationId;

        @NotNull(message = "试用天数不能为空")
        @Min(value = 1, message = "试用天数不能少于1天")
        @Max(value = 365, message = "试用天数不能超过365天")
        private Integer duration;    // 原daysCount改为duration
    }

    /**
     * 权限开通历史VO
     */
    @Data
    public static class PermissionGrantHistoryVO {
        @ApiModelProperty(value = "记录ID")
        private String id;

        @ApiModelProperty(value = "用户ID")
        private String userId;

        @ApiModelProperty(value = "操作者ID")
        private String operatorId;

        @ApiModelProperty(value = "操作类型")
        private String operationType;

        @ApiModelProperty(value = "操作描述")
        private String description;

        @ApiModelProperty(value = "天数变化")
        private Integer duration;

        @ApiModelProperty(value = "教育系列ID")
        private String educationId;

        @ApiModelProperty(value = "操作前剩余天数")
        private Integer beforeDays;

        @ApiModelProperty(value = "操作后剩余天数")
        private Integer afterDays;

        @ApiModelProperty(value = "创建时间")
        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        private Date createTime;

        @ApiModelProperty(value = "来源类型")
        private String sourceType;
    }
}

