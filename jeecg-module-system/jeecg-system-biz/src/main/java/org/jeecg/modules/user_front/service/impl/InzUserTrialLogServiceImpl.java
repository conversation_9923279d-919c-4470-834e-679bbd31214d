package org.jeecg.modules.user_front.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.user_front.entity.InzUserFront;
import org.jeecg.modules.user_front.entity.InzUserTrialLog;
import org.jeecg.modules.user_front.mapper.InzUserTrialLogMapper;
import org.jeecg.modules.user_front.service.IInzUserFrontService;
import org.jeecg.modules.user_front.service.IInzUserTrialLogService;
import org.jeecg.modules.config.entity.InzConfig;
import org.jeecg.modules.config.service.IInzConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;


/**
 * @Description: 用户试用记录Service实现
 * @Author: Alex (工程师)
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Service
@Slf4j
public class InzUserTrialLogServiceImpl extends ServiceImpl<InzUserTrialLogMapper, InzUserTrialLog> implements IInzUserTrialLogService {

    @Autowired
    private IInzConfigService inzConfigService;

    @Autowired
    private IInzUserFrontService inzUserFrontService;

    @Override
    public List<InzUserTrialLog> selectByUserId(String userId) {
        return baseMapper.selectByUserId(userId);
    }

    @Override
    public Integer calculateRemainingDays(String userId) {
        try {
            // 直接从用户表获取当前剩余天数
            LambdaQueryWrapper<InzUserFront> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(InzUserFront::getId, userId);
            InzUserFront userFront = inzUserFrontService.getOne(wrapper);

            if (userFront == null) {
                log.warn("用户不存在 - 用户ID: {}", userId);
                return 0;
            }

            Integer remainingDays = userFront.getTrialRemainingDays();
            if (remainingDays == null) {
                remainingDays = 0;
            }

            log.debug("获取用户剩余试用天数 - 用户: {}, 剩余天数: {}", userId, remainingDays);
            return remainingDays;
        } catch (Exception e) {
            log.error("获取用户剩余试用天数失败 - 用户: {}", userId, e);
            return 0;
        }
    }

    /**
     * 获取最大试用天数配置
     */
    private Integer getMaxTrialDays() {
        try {
            QueryWrapper<InzConfig> wrapper = new QueryWrapper<>();
            wrapper.eq("code", "FREE_MEMBERSHIP_DAYS");
            InzConfig config = inzConfigService.getOne(wrapper);

            if (config != null && config.getValue() != null) {
                return Integer.parseInt(config.getValue().trim());
            }

            log.warn("FREE_MEMBERSHIP_DAYS配置不存在，使用默认值30天");
            return 30; // 默认30天
        } catch (Exception e) {
            log.error("获取最大试用天数配置失败，使用默认值30天", e);
            return 30;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addTrialDays(String userId, String operatorId, Integer days, String content, String educationSeriesId) {
        try {
            // 1. 获取当前剩余天数
            Integer beforeDays = calculateRemainingDays(userId);
            Integer afterDays = beforeDays + days;

            // 2. 创建试用记录
            InzUserTrialLog trialLog = new InzUserTrialLog();
            trialLog.setUserId(userId);
            trialLog.setOperatorId(operatorId);
            trialLog.setContent(content);
            trialLog.setTrialDays(days);
            trialLog.setType(1); // 1表示增加
            trialLog.setBeforeDays(beforeDays);
            trialLog.setAfterDays(afterDays);
            trialLog.setEducationSeriesId(educationSeriesId);
            trialLog.setSourceType("manual");
            trialLog.setCreateTime(new Date());

            boolean success = this.save(trialLog);
            
            if (success) {
                log.info("试用天数增加成功 - 用户: {}, 增加: {}天, 操作者: {}", userId, days, operatorId);
            }
            
            return success;
        } catch (Exception e) {
            log.error("添加试用天数失败 - 用户: {}, 天数: {}", userId, days, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reduceTrialDays(String userId, String operatorId, Integer days, String content) {
        try {
            // 1. 参数验证
            if (StringUtils.isBlank(userId) || days == null || days <= 0) {
                log.error("参数无效 - 用户ID: {}, 天数: {}", userId, days);
                return false;
            }

            // 2. 使用悲观锁获取用户信息，防止并发问题
            LambdaQueryWrapper<InzUserFront> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(InzUserFront::getId, userId);
            InzUserFront userFront = inzUserFrontService.getOne(queryWrapper);

            if (userFront == null) {
                log.error("用户不存在 - 用户ID: {}", userId);
                return false;
            }

            // 3. 获取当前剩余天数（从数据库实时读取）
            Integer beforeDays = userFront.getTrialRemainingDays();
            if (beforeDays == null) {
                beforeDays = 0;
            }

            // 4. 验证剩余天数是否充足
            if (beforeDays < days) {
                log.error("剩余天数不足 - 用户ID: {}, 当前剩余: {}, 需要: {}",
                         userId, beforeDays, days);
                return false;
            }

            // 5. 计算新的剩余天数
            Integer afterDays = beforeDays - days;

            // 6. 创建试用记录
            InzUserTrialLog trialLog = new InzUserTrialLog();
            trialLog.setUserId(userId);
            trialLog.setOperatorId(operatorId);
            trialLog.setContent(content);
            trialLog.setTrialDays(-days); // 负数表示减少
            trialLog.setType(0); // 0表示减少
            trialLog.setBeforeDays(beforeDays);
            trialLog.setAfterDays(afterDays);
            trialLog.setSourceType("agent_grant"); // 标识为代理商操作
            trialLog.setCreateTime(new Date());

            // 7. 原子性操作：先插入记录，再更新用户表
            boolean logSuccess = this.save(trialLog);
            if (!logSuccess) {
                log.error("试用记录保存失败 - 用户ID: {}", userId);
                throw new RuntimeException("试用记录保存失败");
            }

            // 8. 更新用户剩余天数
            userFront.setTrialRemainingDays(afterDays);
            userFront.setTrialLastUpdate(new Date());
            boolean updateSuccess = inzUserFrontService.updateById(userFront);
            if (!updateSuccess) {
                log.error("用户信息更新失败 - 用户ID: {}", userId);
                throw new RuntimeException("用户信息更新失败");
            }

            log.info("试用天数减少成功 - 用户: {}, 减少: {}天, 前: {}, 后: {}, 操作者: {}",
                    userId, days, beforeDays, afterDays, operatorId);

            return true;

        } catch (Exception e) {
            log.error("减少试用天数失败 - 用户: {}, 天数: {}", userId, days, e);
            throw e; // 让事务回滚
        }
    }

    @Override
    public Integer getTotalUsedDays(String userId) {
        try {
            Integer totalDays = baseMapper.getTotalUsedDays(userId);
            log.debug("统计用户已使用试用天数 - 用户: {}, 总天数: {}", userId, totalDays);
            return totalDays != null ? totalDays : 0;
        } catch (Exception e) {
            log.error("统计用户已使用试用天数失败 - 用户: {}", userId, e);
            return 0;
        }
    }

    @Override
    public InzUserTrialLog getLatestRecord(String userId) {
        return baseMapper.getLatestRecord(userId);
    }

    @Override
    public List<InzUserTrialLog> selectByOperatorId(String operatorId) {
        return baseMapper.selectByOperatorId(operatorId);
    }

    @Override
    public List<InzUserTrialLog> selectByTimeRange(String startTime, String endTime) {
        return baseMapper.selectByTimeRange(startTime, endTime);
    }

    @Override
    public boolean hasTrialHistory(String userId) {
        QueryWrapper<InzUserTrialLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        long count = this.count(queryWrapper);
        return count > 0;
    }

    @Override
    public TrialStatistics getTrialStatistics(String userId) {
        try {
            TrialStatistics statistics = new TrialStatistics();
            
            // 统计已使用天数
            Integer totalUsedDays = getTotalUsedDays(userId);
            statistics.setTotalUsedDays(totalUsedDays);
            
            // 计算剩余天数
            Integer remainingDays = calculateRemainingDays(userId);
            statistics.setRemainingDays(remainingDays);
            
            // 统计记录数量
            QueryWrapper<InzUserTrialLog> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            long recordCount = this.count(queryWrapper);
            statistics.setRecordCount((int) recordCount);
            
            // 获取最后操作时间
            InzUserTrialLog latestRecord = getLatestRecord(userId);
            if (latestRecord != null && latestRecord.getCreateTime() != null) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                statistics.setLastOperationTime(sdf.format(latestRecord.getCreateTime()));
            }
            
            return statistics;
        } catch (Exception e) {
            log.error("获取用户试用统计信息失败 - 用户: {}", userId, e);
            return new TrialStatistics();
        }
    }
}
