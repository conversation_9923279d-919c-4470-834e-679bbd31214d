package org.jeecg.modules.user_front.service;

import org.jeecg.modules.user_front.entity.InzUserFront;
import org.jeecg.modules.user_front.entity.InzUserTrialLog;
import org.jeecg.modules.user_front.service.IInzUserFrontService;
import org.jeecg.modules.user_front.service.IInzUserTrialLogService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * trialRemainingDays数据一致性测试
 * 
 * <AUTHOR> (工程师)
 * @date 2025-01-15
 */
@SpringBootTest
@Transactional
@Rollback
public class TrialRemainingDaysTest {

    @Autowired
    private IInzUserTrialLogService trialLogService;
    
    @Autowired
    private IInzUserFrontService userFrontService;

    /**
     * 测试正常减少试用天数场景
     */
    @Test
    public void testReduceTrialDaysNormal() {
        // 1. 准备测试数据
        String testUserId = "test_user_001";
        String operatorId = "test_operator_001";
        Integer initialDays = 30;
        Integer reduceDays = 5;
        
        // 创建测试用户
        InzUserFront testUser = createTestUser(testUserId, initialDays);
        userFrontService.save(testUser);
        
        // 2. 执行减少操作
        boolean result = trialLogService.reduceTrialDays(
            testUserId, 
            operatorId, 
            reduceDays, 
            "测试减少试用天数"
        );
        
        // 3. 验证结果
        assertTrue(result, "减少试用天数应该成功");
        
        // 4. 验证用户表数据
        InzUserFront updatedUser = userFrontService.getById(testUserId);
        assertNotNull(updatedUser, "用户应该存在");
        assertEquals(initialDays - reduceDays, updatedUser.getTrialRemainingDays(), 
                    "用户剩余天数应该正确减少");
        
        // 5. 验证日志记录
        List<InzUserTrialLog> logs = trialLogService.selectByUserId(testUserId);
        assertEquals(1, logs.size(), "应该有一条日志记录");
        
        InzUserTrialLog log = logs.get(0);
        assertEquals(testUserId, log.getUserId(), "日志用户ID应该正确");
        assertEquals(operatorId, log.getOperatorId(), "日志操作者ID应该正确");
        assertEquals(-reduceDays, log.getTrialDays(), "日志天数变化应该为负数");
        assertEquals(0, log.getType(), "日志类型应该为0（减少）");
        assertEquals(initialDays, log.getBeforeDays(), "日志操作前天数应该正确");
        assertEquals(initialDays - reduceDays, log.getAfterDays(), "日志操作后天数应该正确");
        assertEquals("agent_grant", log.getSourceType(), "日志来源类型应该正确");
    }

    /**
     * 测试天数不足场景
     */
    @Test
    public void testReduceTrialDaysInsufficientDays() {
        // 1. 准备测试数据
        String testUserId = "test_user_002";
        String operatorId = "test_operator_002";
        Integer initialDays = 5;
        Integer reduceDays = 10; // 超过剩余天数
        
        // 创建测试用户
        InzUserFront testUser = createTestUser(testUserId, initialDays);
        userFrontService.save(testUser);
        
        // 2. 执行减少操作
        boolean result = trialLogService.reduceTrialDays(
            testUserId, 
            operatorId, 
            reduceDays, 
            "测试天数不足场景"
        );
        
        // 3. 验证结果
        assertFalse(result, "天数不足时减少操作应该失败");
        
        // 4. 验证用户表数据未变化
        InzUserFront unchangedUser = userFrontService.getById(testUserId);
        assertEquals(initialDays, unchangedUser.getTrialRemainingDays(), 
                    "天数不足时用户剩余天数不应该变化");
        
        // 5. 验证没有日志记录
        List<InzUserTrialLog> logs = trialLogService.selectByUserId(testUserId);
        assertEquals(0, logs.size(), "天数不足时不应该有日志记录");
    }

    /**
     * 测试用户不存在场景
     */
    @Test
    public void testReduceTrialDaysUserNotExists() {
        // 1. 准备测试数据
        String nonExistentUserId = "non_existent_user";
        String operatorId = "test_operator_003";
        Integer reduceDays = 5;
        
        // 2. 执行减少操作
        boolean result = trialLogService.reduceTrialDays(
            nonExistentUserId, 
            operatorId, 
            reduceDays, 
            "测试用户不存在场景"
        );
        
        // 3. 验证结果
        assertFalse(result, "用户不存在时减少操作应该失败");
    }

    /**
     * 测试参数无效场景
     */
    @Test
    public void testReduceTrialDaysInvalidParams() {
        String operatorId = "test_operator_004";
        
        // 测试用户ID为空
        boolean result1 = trialLogService.reduceTrialDays(null, operatorId, 5, "测试");
        assertFalse(result1, "用户ID为空时应该失败");
        
        // 测试用户ID为空字符串
        boolean result2 = trialLogService.reduceTrialDays("", operatorId, 5, "测试");
        assertFalse(result2, "用户ID为空字符串时应该失败");
        
        // 测试天数为null
        boolean result3 = trialLogService.reduceTrialDays("test_user", operatorId, null, "测试");
        assertFalse(result3, "天数为null时应该失败");
        
        // 测试天数为0
        boolean result4 = trialLogService.reduceTrialDays("test_user", operatorId, 0, "测试");
        assertFalse(result4, "天数为0时应该失败");
        
        // 测试天数为负数
        boolean result5 = trialLogService.reduceTrialDays("test_user", operatorId, -5, "测试");
        assertFalse(result5, "天数为负数时应该失败");
    }

    /**
     * 测试calculateRemainingDays方法
     */
    @Test
    public void testCalculateRemainingDays() {
        // 1. 准备测试数据
        String testUserId = "test_user_005";
        Integer initialDays = 25;
        
        // 创建测试用户
        InzUserFront testUser = createTestUser(testUserId, initialDays);
        userFrontService.save(testUser);
        
        // 2. 测试计算剩余天数
        Integer remainingDays = trialLogService.calculateRemainingDays(testUserId);
        
        // 3. 验证结果
        assertEquals(initialDays, remainingDays, "计算的剩余天数应该正确");
        
        // 4. 测试用户不存在的情况
        Integer remainingDaysForNonExistent = trialLogService.calculateRemainingDays("non_existent");
        assertEquals(0, remainingDaysForNonExistent, "用户不存在时应该返回0");
    }

    /**
     * 创建测试用户
     */
    private InzUserFront createTestUser(String userId, Integer trialRemainingDays) {
        InzUserFront user = new InzUserFront();
        user.setId(userId);
        user.setRealName("测试用户");
        user.setPhone("13800000000");
        user.setStatus(1);
        user.setTrialRemainingDays(trialRemainingDays);
        user.setTrialTotalDays(0);
        user.setTrialLastUpdate(new Date());
        user.setCreateTime(new Date());
        return user;
    }
}
